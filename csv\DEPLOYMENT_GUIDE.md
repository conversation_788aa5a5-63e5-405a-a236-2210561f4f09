# Tufin Rule Recertification - Deployment Guide

## 📦 Required Files for Production Deployment

### Core System Files (Required)
```
tufin_rule_recertification.py    # Main orchestration script
tufin_api_client.py             # Tufin API client and authentication  
rule_owner_manager.py           # Rule ownership detection and validation
securechange_ticket_manager.py  # SecureChange ticket creation and management
requirements.txt               # Python dependencies
```

### Configuration Files (Required)
```
tufin_config.json              # Configuration template (customize for your environment)
```

### Documentation (Recommended)
```
TUFIN_README.md               # Comprehensive usage guide
DEPLOYMENT_GUIDE.md           # This deployment guide
```

### Optional Files (Not needed for production)
```
tufin_example_usage.py        # Usage examples and demonstrations
dummy_tufin_data.json         # Test data (for local testing only)
mock_tufin_api_client.py      # Mock client (for local testing only)
test_*.py                     # Test scripts (for development only)
run_local_test.py            # Local testing script (for development only)
test_config.json             # Test configuration (for development only)
*.log                        # Log files (generated during execution)
test_results_*.json          # Test result files (generated during testing)
```

## 🚀 Quick Deployment Steps

### 1. Download Core Files
Copy only the required files to your production server:
```bash
# Create deployment directory
mkdir tufin_recertification
cd tufin_recertification

# Copy core files (adjust paths as needed)
cp tufin_rule_recertification.py .
cp tufin_api_client.py .
cp rule_owner_manager.py .
cp securechange_ticket_manager.py .
cp requirements.txt .
cp tufin_config.json .
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure System
```bash
# Create your configuration
cp tufin_config.json production_config.json

# Edit with your environment details
nano production_config.json
```

### 4. Test Configuration
```bash
# Test with dry run
python tufin_rule_recertification.py --config production_config.json --dry-run
```

### 5. Schedule Execution
Set up automated execution (daily recommended):

**Linux/Unix (crontab):**
```bash
# Run daily at 9 AM
0 9 * * * /usr/bin/python3 /path/to/tufin_rule_recertification.py --config /path/to/production_config.json
```

**Windows (Task Scheduler):**
Create a batch file and schedule it:
```batch
@echo off
cd /d "C:\path\to\tufin_recertification"
python tufin_rule_recertification.py --config production_config.json
```

## 📋 Configuration Checklist

### Required Configuration Items
- [ ] Tufin server URL and credentials
- [ ] Default owner email address
- [ ] SSL verification settings
- [ ] Timeout values

### Recommended Configuration Items  
- [ ] Owner mapping for device types
- [ ] Pattern-based owner detection rules
- [ ] Custom ticket templates
- [ ] Logging configuration

### Security Checklist
- [ ] Use secure credential storage
- [ ] Enable SSL verification in production
- [ ] Restrict file permissions (600 for config files)
- [ ] Monitor log files for security events
- [ ] Regular credential rotation

## 🔍 Validation Steps

### Pre-Deployment Testing
1. **Connectivity Test**: Verify API access to Tufin server
2. **Dry Run**: Test rule discovery and owner detection without creating tickets
3. **Small Batch**: Test with limited scope (single device or short time range)
4. **Full Test**: Complete test with ticket creation in test environment

### Post-Deployment Monitoring
1. **Log Monitoring**: Check logs for errors and warnings
2. **Ticket Verification**: Verify tickets are created and assigned correctly
3. **Owner Feedback**: Confirm owners receive and can access tickets
4. **Performance Monitoring**: Monitor execution time and resource usage

## 📊 Expected File Sizes
```
tufin_rule_recertification.py    ~15-20 KB
tufin_api_client.py             ~25-30 KB  
rule_owner_manager.py           ~15-20 KB
securechange_ticket_manager.py  ~10-15 KB
requirements.txt               ~1 KB
tufin_config.json              ~2-5 KB (depending on customization)
```

## 🔧 Minimal System Requirements
- **Python**: 3.7 or higher
- **Memory**: 256 MB RAM minimum
- **Disk**: 100 MB free space
- **Network**: HTTPS access to Tufin server
- **Permissions**: Read/write access to working directory

## 📞 Support
For deployment issues:
1. Check the troubleshooting section in TUFIN_README.md
2. Review log files for detailed error messages
3. Test individual components in isolation
4. Verify network connectivity and permissions
