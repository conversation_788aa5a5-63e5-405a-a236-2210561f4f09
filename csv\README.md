# 🔒 CSV Security Validation Pipeline

A comprehensive security validation system for analyzing network flow data from CSV files. This tool validates network configurations against security policies and generates detailed reports for Azure DevOps pipelines.

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [System Architecture](#system-architecture)
- [Configuration Files](#configuration-files)
- [Usage Examples](#usage-examples)
- [Pipeline Integration](#pipeline-integration)
- [Adding New Security Rules](#adding-new-security-rules)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

This security validation system:
- ✅ **Analyzes CSV network flow data** for security compliance
- ✅ **Validates against configurable security policies** (JSON-based rules)
- ✅ **Generates comprehensive markdown reports** with risk assessments
- ✅ **Exports data to YAML format** for integration and automation
- ✅ **Integrates with Azure DevOps pipelines** with proper exit codes
- ✅ **Provides detailed security guidance** and recommendations

### Key Features
- **Risk-based classification**: Critical, High, Medium, Low risk levels
- **Configurable validation rules**: JSON-based unacceptable values and guidance
- **Professional reporting**: Markdown reports with metrics and visualizations
- **YAML data export**: Export CSV data and validation results to YAML format
- **Pipeline-ready**: Azure DevOps compatible logging and exit codes
- **Robust error handling**: Graceful handling of malformed data

## 🚀 Quick Start

### Prerequisites
- Python 3.7+
- CSV file with network flow data
- Configuration files (see [Configuration Files](#configuration-files))

### Basic Usage

1. **Prepare your CSV file** with network flow data:
```csv
Source IP,Destination IP,Port,Service,Action
***********,***********0,443,https,Allow
***********,************,22,ssh,Allow
```

2. **Run the validation**:
```bash
python security_validator.py
```

3. **View the generated report**: `security_validation_report.md`

## 🏗️ System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CSV File      │    │ Configuration    │    │ Security        │
│ (Network Flows) │───▶│ Files (JSON)     │───▶│ Validator       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Markdown Report │◀───│ Validation       │◀───│ Risk Assessment │
│ (Comprehensive) │    │ Results          │    │ & Guidance      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Core Components

| Component | Purpose | File |
|-----------|---------|------|
| **CSV Processor** | Parses CSV files into structured data | `csv_processor.py` |
| **Security Validator** | Core validation logic and risk assessment | `security_validator.py` |
| **Pipeline Validator** | Azure DevOps integration with exit codes | `pipeline_validator.py` |
| **Configuration Files** | Security rules and guidance definitions | `*.json` |

## ⚙️ Configuration Files

### 1. `unacceptable_values.json`
Defines values that should trigger security alerts:

```json
{
  "Service": {
    "risky": ["ssh", "telnet", "ftp"],
    "blocked": ["rsh", "rlogin"]
  },
  "Action": {
    "concerning": ["Allow"]
  },
  "Port": {
    "risky": ["23", "21", "135"]
  }
}
```

### 2. `security_guidance.json`
Provides contextual security guidance and recommendations:

```json
{
  "service_guidance": {
    "ssh": {
      "risk_level": "high",
      "message": "SSH is a risky protocol when detected. Ensure proper authentication and monitoring.",
      "recommendations": [
        "Enable key-based authentication",
        "Monitor for brute force attempts",
        "Restrict source IPs"
      ]
    }
  },
  "general_guidance": {
    "Action": {
      "Allow": {
        "message": "Traffic is being allowed - ensure this is intentional and secure.",
        "recommendations": [
          "Review allow rules regularly",
          "Monitor allowed traffic",
          "Implement least privilege"
        ]
      }
    }
  }
}
```

## 💻 Usage Examples

### Standalone Validation
```python
from security_validator import SecurityValidator, generate_markdown_report, export_to_yaml
from csv_processor import csv_to_dict_simple

# Load and validate
flow_data = csv_to_dict_simple("network_flows.csv")
validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
results = validator.validate_flow_data(flow_data)

# Generate reports
report_file = generate_markdown_report(results, "network_flows.csv")
yaml_file = export_to_yaml(results, "network_flows.csv")
print(f"Report generated: {report_file}")
print(f"YAML export generated: {yaml_file}")
```

### Raw CSV Data Export
```python
from csv_processor import csv_to_dict_simple
from security_validator import export_csv_data_to_yaml

# Export CSV data to YAML without validation
flow_data = csv_to_dict_simple("network_flows.csv")
yaml_file = export_csv_data_to_yaml(flow_data, "csv_data.yaml")
print(f"CSV data exported to: {yaml_file}")
```

### Pipeline Integration
```bash
# Basic pipeline validation
python pipeline_validator.py --csv flows.csv --unacceptable rules.json --guidance guidance.json

# Fail on critical issues only
python pipeline_validator.py --csv flows.csv --unacceptable rules.json --guidance guidance.json --fail-on-critical

# Export results to JSON
python pipeline_validator.py --csv flows.csv --unacceptable rules.json --guidance guidance.json --output-json results.json
```

## 🔄 Pipeline Integration

### Azure DevOps YAML Pipeline

```yaml
- task: PythonScript@0
  displayName: 'Security Validation'
  inputs:
    scriptSource: 'filePath'
    scriptPath: 'security/pipeline_validator.py'
    arguments: '--csv $(csvFile) --unacceptable $(unacceptableRules) --guidance $(securityGuidance) --fail-on-critical'
  continueOnError: false

- task: PublishBuildArtifacts@1
  displayName: 'Publish Security Report'
  inputs:
    pathToPublish: 'security_validation_report.md'
    artifactName: 'SecurityReport'
```

### Exit Codes
- **0**: Validation passed (no critical issues)
- **1**: Validation failed (critical issues found or system error)

### Pipeline Variables Set
- `criticalFlows`: Comma-separated list of flows with critical issues
- `highRiskFlows`: Comma-separated list of flows with high-risk issues
- `totalIssues`: Total number of flows with issues
- `criticalIssues`: Number of critical issues found
- `securityReportPath`: Path to generated markdown report

## 🔧 Adding New Security Rules

### Adding Unacceptable Values

1. **Edit `unacceptable_values.json`**:
```json
{
  "Service": {
    "risky": ["ssh", "telnet", "ftp", "new_risky_service"],
    "blocked": ["rsh", "rlogin", "new_blocked_service"]
  },
  "NewField": {
    "critical": ["dangerous_value"],
    "concerning": ["questionable_value"]
  }
}
```

2. **Risk Level Mapping**:
   - `blocked` → **Critical** risk
   - `risky` → **High** risk
   - `concerning` → **Medium** risk
   - `deprecated` → **Low** risk

### Adding Security Guidance

1. **Edit `security_guidance.json`**:
```json
{
  "service_guidance": {
    "new_service": {
      "risk_level": "high",
      "message": "Description of the security concern.",
      "recommendations": [
        "Specific action 1",
        "Specific action 2"
      ]
    }
  },
  "general_guidance": {
    "NewField": {
      "problematic_value": {
        "message": "Why this value is concerning.",
        "recommendations": ["How to fix it"]
      }
    }
  }
}
```

### CSV Field Requirements

Your CSV file should include these standard fields:
- `Source IP`
- `Destination IP`
- `Port`
- `Service`
- `Action`

**Adding new fields**: Simply add columns to your CSV and corresponding rules to the JSON files. The system will automatically validate any field present in both the CSV and configuration files.

### Example: Adding Database Security Rules

1. **Add to `unacceptable_values.json`**:
```json
{
  "Database": {
    "risky": ["mysql", "postgresql"],
    "blocked": ["mongodb_unencrypted"]
  },
  "Encryption": {
    "critical": ["none", "disabled"],
    "concerning": ["weak"]
  }
}
```

2. **Add to `security_guidance.json`**:
```json
{
  "service_guidance": {
    "mysql": {
      "risk_level": "high",
      "message": "MySQL requires proper authentication and encryption.",
      "recommendations": [
        "Enable SSL/TLS encryption",
        "Use strong authentication",
        "Restrict network access"
      ]
    }
  },
  "general_guidance": {
    "Encryption": {
      "none": {
        "message": "Unencrypted database connections pose significant security risks.",
        "recommendations": [
          "Enable database encryption",
          "Use TLS for connections",
          "Implement encryption at rest"
        ]
      }
    }
  }
}
```

3. **Update your CSV**:
```csv
Source IP,Destination IP,Port,Service,Action,Database,Encryption
********,**********,3306,mysql,Allow,mysql,none
```

## 🐛 Troubleshooting

### Common Issues

**KeyError: 'flows_with_issues'**
- ✅ **Fixed**: The system now handles invalid CSV data gracefully
- Check that your CSV file exists and has valid data
- Verify CSV headers match expected field names

**No validation rules applied**
- Ensure field names in CSV match those in `unacceptable_values.json`
- Field names are case-sensitive
- Check JSON file syntax with a JSON validator

**Report not generated**
- Verify write permissions in the output directory
- Check that all required files exist
- Review console output for specific error messages

### Debug Mode

Run with detailed debugging:
```python
from security_validator import SecurityValidator

validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
results = validator.validate_flow_data(flow_data)

# Check for errors
if results.get('errors'):
    print("Validation errors:")
    for error in results['errors']:
        print(f"  - {error}")
```

### Validation Checklist

- [ ] CSV file exists and is readable
- [ ] CSV has required headers (Source IP, Destination IP, Port, Service, Action)
- [ ] `unacceptable_values.json` exists and is valid JSON
- [ ] `security_guidance.json` exists and is valid JSON
- [ ] Field names in CSV match those in JSON configuration files
- [ ] Python dependencies are installed

## 📞 Support

For issues or questions:
1. Check the [Troubleshooting](#troubleshooting) section
2. Review generated error messages in console output
3. Verify configuration file syntax
4. Check CSV file format and field names

---

*CSV Security Validation Pipeline - Ensuring network security through automated validation*
