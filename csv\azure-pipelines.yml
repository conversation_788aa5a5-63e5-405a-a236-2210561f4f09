# Azure DevOps Pipeline for CSV Security Validation
# This pipeline validates CSV files against security policies

trigger:
- main
- develop

pool:
  vmImage: 'ubuntu-latest'

variables:
  pythonVersion: '3.11'

stages:
- stage: SecurityValidation
  displayName: 'CSV Security Validation'
  jobs:
  - job: ValidateCSV
    displayName: 'Validate CSV Security'
    steps:
    
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '$(pythonVersion)'
      displayName: 'Use Python $(pythonVersion)'
    
    - script: |
        python -m pip install --upgrade pip
        # Add any additional dependencies here if needed
      displayName: 'Install Python dependencies'
    
    - script: |
        echo "Validating CSV files for security compliance..."
        python pipeline_validator.py \
          --csv "Example.csv" \
          --unacceptable "unacceptable_values.json" \
          --guidance "security_guidance.json" \
          --fail-on-critical \
          --output-json "$(Agent.TempDirectory)/validation_results.json"
      displayName: 'Run Security Validation'
      continueOnError: false
    
    - script: |
        echo "Validation completed. Check results above."
        echo "Critical Issues: $(criticalIssues)"
        echo "High Risk Issues: $(highRiskIssues)" 
        echo "Total Issues: $(totalIssues)"
        if [ "$(criticalIssues)" -gt "0" ]; then
          echo "##[error]Critical security issues found - pipeline should fail"
        fi
        if [ "$(highRiskIssues)" -gt "0" ]; then
          echo "##[warning]High risk security issues found"
        fi
      displayName: 'Display Validation Summary'
    
    - task: PublishTestResults@2
      condition: always()
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '$(Agent.TempDirectory)/validation_results.json'
        failTaskOnFailedTests: false
      displayName: 'Publish Validation Results'
    
    - task: PublishBuildArtifacts@1
      condition: always()
      inputs:
        pathToPublish: '$(Agent.TempDirectory)/validation_results.json'
        artifactName: 'SecurityValidationResults'
      displayName: 'Publish Security Results Artifact'

# Optional: Additional stage for handling security issues
- stage: SecurityRemediation
  displayName: 'Security Issue Handling'
  condition: and(succeeded(), gt(variables['criticalIssues'], 0))
  dependsOn: SecurityValidation
  jobs:
  - job: HandleCriticalIssues
    displayName: 'Handle Critical Security Issues'
    steps:
    - script: |
        echo "Critical security issues detected!"
        echo "Critical Flows: $(criticalFlows)"
        echo "Initiating security incident response..."
        # Add your incident response logic here
        # e.g., send notifications, create tickets, etc.
      displayName: 'Security Incident Response'
