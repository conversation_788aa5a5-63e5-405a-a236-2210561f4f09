#!/usr/bin/env python3
"""
Create Excel template with built-in data validation controls.
This script creates an Excel file with data validation rules that prevent invalid data entry.
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Font, Alignment
from openpyxl.comments import Comment

def create_network_flow_template():
    """Create Excel template with comprehensive data validation."""
    
    print("🔧 Creating Network Flow Excel Template with Validation...")
    
    # Create workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Network Flows"
    
    # Define headers
    headers = [
        "Source IP", "Destination IP", "Port", "Service", "Action", "File Type", "Description"
    ]
    
    # Set up headers with formatting
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
    
    # Add data validation rules
    setup_ip_validation(ws, "A", "Source IP")  # Column A
    setup_ip_validation(ws, "B", "Destination IP")  # Column B
    setup_port_validation(ws, "C")  # Column C
    setup_service_validation(ws, "D")  # Column D
    setup_action_validation(ws, "E")  # Column E
    setup_file_type_validation(ws, "F")  # Column F
    
    # Add sample data with comments
    add_sample_data_with_instructions(ws)
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 20)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Create instructions sheet
    create_instructions_sheet(wb)
    
    # Save the template
    filename = "Network_Flow_Template.xlsx"
    wb.save(filename)
    print(f"✅ Created Excel template: {filename}")
    
    return filename

def setup_ip_validation(ws, column_letter, field_name):
    """Set up IP address validation for a column."""
    
    # Create data validation for IP addresses
    ip_validation = DataValidation(
        type="custom",
        formula1=f'AND(LEN({column_letter}2)>6,LEN({column_letter}2)<16,LEN({column_letter}2)-LEN(SUBSTITUTE({column_letter}2,".",""))=3)',
        allow_blank=False
    )
    
    ip_validation.error = f"Invalid {field_name}"
    ip_validation.errorTitle = "IP Address Error"
    ip_validation.prompt = f"Enter a valid IP address (e.g., ***********)"
    ip_validation.promptTitle = f"{field_name} Format"
    
    # Apply to range (rows 2-1000)
    ws.add_data_validation(ip_validation)
    ip_validation.add(f"{column_letter}2:{column_letter}1000")

def setup_port_validation(ws, column_letter):
    """Set up port number validation."""
    
    # Allow single ports or comma-separated ports
    port_validation = DataValidation(
        type="custom",
        formula1=f'OR(AND(ISNUMBER(VALUE({column_letter}2)),VALUE({column_letter}2)>=1,VALUE({column_letter}2)<=65535),LEN({column_letter}2)>LEN(SUBSTITUTE({column_letter}2,",","")))',
        allow_blank=False
    )
    
    port_validation.error = "Port must be 1-65535 or comma-separated ports (e.g., 22,80,443)"
    port_validation.errorTitle = "Invalid Port"
    port_validation.prompt = "Enter port number (1-65535) or comma-separated ports"
    port_validation.promptTitle = "Port Number"
    
    ws.add_data_validation(port_validation)
    port_validation.add(f"{column_letter}2:{column_letter}1000")

def setup_service_validation(ws, column_letter):
    """Set up service validation with dropdown."""
    
    # Define valid services
    services = "http,https,ssh,ftp,telnet,smtp,dns,dhcp,custom"
    
    service_validation = DataValidation(
        type="list",
        formula1=f'"{services}"',
        allow_blank=False
    )
    
    service_validation.error = f"Service must be one of: {services} or comma-separated"
    service_validation.errorTitle = "Invalid Service"
    service_validation.prompt = "Select service or enter comma-separated services"
    service_validation.promptTitle = "Service Selection"
    
    ws.add_data_validation(service_validation)
    service_validation.add(f"{column_letter}2:{column_letter}1000")

def setup_action_validation(ws, column_letter):
    """Set up action validation with dropdown."""
    
    action_validation = DataValidation(
        type="list",
        formula1='"Allow,Deny,Block"',
        allow_blank=False
    )
    
    action_validation.error = "Action must be Allow, Deny, or Block"
    action_validation.errorTitle = "Invalid Action"
    action_validation.prompt = "Select Allow, Deny, or Block"
    action_validation.promptTitle = "Action Selection"
    
    ws.add_data_validation(action_validation)
    action_validation.add(f"{column_letter}2:{column_letter}1000")

def setup_file_type_validation(ws, column_letter):
    """Set up file type validation."""
    
    # Allow alphanumeric file extensions
    file_validation = DataValidation(
        type="custom",
        formula1=f'OR({column_letter}2="",AND(LEN({column_letter}2)>0,ISERROR(FIND(" ",{column_letter}2))))',
        allow_blank=True
    )
    
    file_validation.error = "File type must be alphanumeric (e.g., pdf, exe, dll) or comma-separated"
    file_validation.errorTitle = "Invalid File Type"
    file_validation.prompt = "Enter file extension(s) - alphanumeric only"
    file_validation.promptTitle = "File Type"
    
    ws.add_data_validation(file_validation)
    file_validation.add(f"{column_letter}2:{column_letter}1000")

def add_sample_data_with_instructions(ws):
    """Add sample data with instructional comments."""
    
    # Sample data
    sample_data = [
        ["***********", "***********0", "443", "https", "Allow", "pdf", "Web traffic"],
        ["***********", "***********0", "22,80", "ssh,http", "Allow", "exe,dll", "Multiple services"],
        ["***********", "***********0", "21", "ftp", "Deny", "bat", "File transfer blocked"]
    ]
    
    # Add sample data
    for row_idx, row_data in enumerate(sample_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    # Add instructional comments
    comments = [
        (2, 1, "Valid IP format: xxx.xxx.xxx.xxx"),
        (2, 3, "Single port or comma-separated: 22,80,443"),
        (2, 4, "Use dropdown or comma-separated: ssh,http"),
        (2, 5, "Must be Allow, Deny, or Block"),
        (2, 6, "Alphanumeric only: pdf, exe, dll")
    ]
    
    for row, col, comment_text in comments:
        cell = ws.cell(row=row, column=col)
        cell.comment = Comment(comment_text, "Template")

def create_instructions_sheet(wb):
    """Create a separate sheet with detailed instructions."""
    
    instructions_ws = wb.create_sheet("Instructions")
    
    instructions = [
        ["Network Flow Data Entry Instructions", ""],
        ["", ""],
        ["Required Fields:", ""],
        ["• Source IP", "Valid IPv4 address (e.g., ***********)"],
        ["• Destination IP", "Valid IPv4 address (e.g., ********)"],
        ["• Port", "Number 1-65535 or comma-separated (e.g., 22,80,443)"],
        ["• Service", "Select from dropdown or comma-separated (e.g., ssh,http)"],
        ["• Action", "Must be Allow, Deny, or Block"],
        ["", ""],
        ["Optional Fields:", ""],
        ["• File Type", "Alphanumeric extensions (e.g., pdf, exe, dll)"],
        ["• Description", "Free text description"],
        ["", ""],
        ["Validation Rules:", ""],
        ["• IP addresses must be valid IPv4 format"],
        ["• Ports must be between 1 and 65535"],
        ["• Services must be from approved list or custom"],
        ["• Actions are restricted to three values"],
        ["• File types must be alphanumeric only"],
        ["• Comma-separated values are supported for most fields"],
        ["", ""],
        ["Error Handling:", ""],
        ["• Invalid entries will be highlighted in red"],
        ["• Hover over cells for validation messages"],
        ["• File cannot be saved with validation errors"],
        ["• Use the validation button to check all data"],
        ["", ""],
        ["VBA Macros:", ""],
        ["• Real-time validation as you type"],
        ["• Automatic error highlighting"],
        ["• Save prevention with validation errors"],
        ["• Manual validation trigger available"]
    ]
    
    # Add instructions
    for row_idx, (instruction, detail) in enumerate(instructions, 1):
        instructions_ws.cell(row=row_idx, column=1, value=instruction)
        instructions_ws.cell(row=row_idx, column=2, value=detail)
        
        # Format headers
        if instruction and not detail:
            cell = instructions_ws.cell(row=row_idx, column=1)
            cell.font = Font(bold=True, size=12)
    
    # Auto-adjust column widths
    instructions_ws.column_dimensions['A'].width = 25
    instructions_ws.column_dimensions['B'].width = 50

def create_vba_instructions():
    """Create instructions for adding VBA macros."""
    
    instructions = """
VBA MACRO SETUP INSTRUCTIONS
============================

To add real-time validation to your Excel template:

1. Open the Excel file (Network_Flow_Template.xlsx)
2. Press Alt + F11 to open VBA Editor
3. In the Project Explorer, find your workbook
4. Right-click on the worksheet name and select "View Code"
5. Copy and paste the VBA code from 'excel_validation_macros.vba'
6. Save the file as .xlsm (Excel Macro-Enabled Workbook)

FEATURES ENABLED:
• Real-time validation as users type
• Automatic error highlighting (red background)
• Prevention of saving with validation errors
• Dropdown menus for Service and Action fields
• Detailed error messages and tooltips

USAGE:
• Users will see immediate feedback for invalid entries
• Invalid cells are highlighted in red with error messages
• Save operation is blocked until all errors are fixed
• Manual validation can be triggered with ValidateDataManually() macro

SECURITY NOTE:
• Users must enable macros when opening the file
• Consider signing the macros for enterprise deployment
"""
    
    with open("VBA_Setup_Instructions.txt", "w") as f:
        f.write(instructions)
    
    print("✅ Created VBA_Setup_Instructions.txt")

def main():
    """Main function to create the complete Excel validation solution."""
    
    print("🚀 Creating Excel Validation Solution")
    print("=" * 50)
    
    try:
        # Create the Excel template
        template_file = create_network_flow_template()
        
        # Create VBA instructions
        create_vba_instructions()
        
        print(f"\n✅ Excel Validation Solution Created!")
        print(f"📁 Files created:")
        print(f"   • {template_file} - Excel template with built-in validation")
        print(f"   • excel_validation_macros.vba - VBA code for real-time validation")
        print(f"   • VBA_Setup_Instructions.txt - Setup instructions")
        
        print(f"\n🔧 Next Steps:")
        print(f"1. Open {template_file}")
        print(f"2. Follow VBA_Setup_Instructions.txt to add macros")
        print(f"3. Save as .xlsm file")
        print(f"4. Distribute to users")
        
        print(f"\n💡 Features:")
        print(f"   • Built-in data validation rules")
        print(f"   • Dropdown menus for constrained fields")
        print(f"   • Real-time error checking (with VBA)")
        print(f"   • Save prevention with validation errors")
        print(f"   • Comprehensive user instructions")
        
    except ImportError:
        print("❌ Error: openpyxl not installed")
        print("Install with: pip install openpyxl")
    except Exception as e:
        print(f"❌ Error creating template: {e}")

if __name__ == "__main__":
    main()
