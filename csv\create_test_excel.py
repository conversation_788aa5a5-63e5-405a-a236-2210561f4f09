#!/usr/bin/env python3
"""
Create test Excel files for validation testing.
"""

import pandas as pd

def create_valid_excel():
    """Create a valid Excel file that should pass validation."""
    data = {
        'Source IP': ['***********', '***********', '***********'],
        'Destination IP': ['************', '***********0', '***********0'],
        'Port': ['443', '22,80', '21'],
        'Service': ['https', 'ssh,http', 'ftp'],
        'Action': ['Allow', 'Allow', 'Deny'],
        'File Type': ['pdf', 'exe,dll', 'bat']
    }
    
    df = pd.DataFrame(data)
    df.to_excel('test_valid.xlsx', index=False)
    print("✅ Created test_valid.xlsx")

def create_invalid_excel():
    """Create an invalid Excel file that should fail validation."""
    data = {
        'Source IP': ['***********', 'invalid_ip', ''],  # Invalid IP and empty
        'Destination IP': ['************', '***********0', '***********0'],
        'Port': ['443', '99999', 'not_a_port'],  # Invalid port numbers
        'Service': ['https', 'invalid_service', 'ftp'],  # Invalid service
        'Action': ['Allow', 'Maybe', 'Deny'],  # Invalid action
        'File Type': ['pdf', 'exe,dll', 'bat@#$']  # Invalid file extension
    }
    
    df = pd.DataFrame(data)
    df.to_excel('test_invalid.xlsx', index=False)
    print("✅ Created test_invalid.xlsx")

def create_missing_columns_excel():
    """Create an Excel file missing required columns."""
    data = {
        'Source IP': ['***********', '***********'],
        'Port': ['443', '22'],
        # Missing Destination IP, Service, Action
    }
    
    df = pd.DataFrame(data)
    df.to_excel('test_missing_columns.xlsx', index=False)
    print("✅ Created test_missing_columns.xlsx")

if __name__ == "__main__":
    print("🔧 Creating test Excel files...")
    create_valid_excel()
    create_invalid_excel()
    create_missing_columns_excel()
    print("✅ All test files created")
