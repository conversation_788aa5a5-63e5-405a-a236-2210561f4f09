#!/usr/bin/env python3
"""
Simple example script for exporting CSV data to YAML format.
This demonstrates the export_csv_data_to_yaml function usage.
"""

from csv_processor import csv_to_dict_simple
from security_validator import export_csv_data_to_yaml

def main():
    """Export CSV data to YAML format."""
    
    print("📁 CSV to YAML Export")
    print("=" * 30)
    
    # Configuration
    csv_file = "Example.csv"
    output_file = "csv_data_export.yaml"
    
    try:
        # Load CSV data
        print(f"Loading CSV file: {csv_file}")
        flow_data = csv_to_dict_simple(csv_file)
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Export to YAML
        print(f"Exporting to YAML: {output_file}")
        yaml_file = export_csv_data_to_yaml(flow_data, output_file)
        print(f"✅ Export completed: {yaml_file}")
        
        # Show summary
        print(f"\n📊 Export Summary:")
        print(f"  • Source CSV: {csv_file}")
        print(f"  • Output YAML: {yaml_file}")
        print(f"  • Total flows: {len(flow_data)}")
        
        # Show sample of exported data
        print(f"\n📄 Sample YAML content (first 15 lines):")
        with open(yaml_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 15:
                    print("  ...")
                    break
                print(f"  {line.rstrip()}")
        
    except FileNotFoundError:
        print(f"❌ Error: CSV file '{csv_file}' not found")
        print("Make sure the CSV file exists in the current directory")
        
    except Exception as e:
        print(f"❌ Error during export: {e}")

if __name__ == "__main__":
    main()
