#!/usr/bin/env python3
"""
Debug script to identify why expansion isn't working.
"""

from security_validator import csv_to_dict_simple, expand_comma_separated_flows

def test_expansion_directly():
    """Test the expansion function directly with known data."""
    
    print("🧪 Testing Expansion Function Directly")
    print("=" * 50)
    
    # Test case that should definitely expand
    test_flow = {
        "Source IP": "***********, ***********",  # Note: spaces after comma
        "Destination IP": "********,********",    # Note: no spaces
        "Port": "tcp\\22, tcp\\443",               # Note: spaces after comma
        "Service": "ssh,http",                     # Note: no spaces
        "Action": "Allow",
        "File Type": "exe"
    }
    
    print(f"Input test flow: {test_flow}")
    
    try:
        expanded = expand_comma_separated_flows(test_flow, "test_flow")
        print(f"\n✅ Expansion completed: {len(expanded)} flows")
        
        for i, flow in enumerate(expanded[:3]):  # Show first 3
            print(f"  Flow {i+1}: {flow}")
            
    except Exception as e:
        print(f"❌ Error in expansion: {e}")
        import traceback
        traceback.print_exc()

def test_csv_loading():
    """Test CSV loading with your problematic file."""
    
    print(f"\n📁 Testing CSV Loading")
    print("=" * 30)
    
    # You should replace this with your actual CSV file name
    csv_file = "test_comma_expansion.csv"  # Change this to your file
    
    try:
        print(f"Loading {csv_file} with expansion=True...")
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        
        print(f"✅ Loaded {len(flow_data)} flows")
        print(f"Flow IDs: {list(flow_data.keys())}")
        
        # Show first flow
        if flow_data:
            first_flow_id, first_flow_data = list(flow_data.items())[0]
            print(f"\nFirst flow: {first_flow_id}")
            for field, value in first_flow_data.items():
                print(f"  {field}: '{value}'")
        
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        import traceback
        traceback.print_exc()

def test_manual_split():
    """Test manual string splitting to see if the issue is in the split logic."""
    
    print(f"\n✂️  Testing Manual String Splitting")
    print("=" * 40)
    
    test_strings = [
        "***********, ***********",
        "tcp\\22, tcp\\443", 
        "ssh,http",
        "exe,dll,bat"
    ]
    
    for test_str in test_strings:
        print(f"\nTesting: '{test_str}'")
        print(f"  Has comma: {',' in test_str}")
        
        if ',' in test_str:
            split_result = [v.strip() for v in test_str.split(',') if v.strip()]
            print(f"  Split result: {split_result}")
            print(f"  Length: {len(split_result)}")
        else:
            print(f"  No comma found")

def check_csv_format():
    """Check the exact format of your CSV file."""

    print(f"\n📋 Checking CSV Format")
    print("=" * 25)

    csv_file = "test_comma_expansion.csv"  # Change this to your file

    try:
        with open(csv_file, 'r') as f:
            for i, line in enumerate(f, 1):
                print(f"Line {i}: {repr(line)}")

                # Check for comma-like characters in each line
                if ',' in line:
                    print(f"  ✅ Has regular comma")
                if '，' in line:
                    print(f"  ⚠️  Has unicode comma")
                if ';' in line:
                    print(f"  ⚠️  Has semicolon")

                # Show bytes for any suspicious characters
                line_bytes = line.encode('utf-8')
                print(f"  Bytes: {line_bytes}")

                if i > 5:  # Only show first 5 lines
                    break
    except FileNotFoundError:
        print(f"❌ File {csv_file} not found")
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def inspect_characters():
    """Inspect specific character patterns that might be causing issues."""

    print(f"\n🔍 Character Inspection")
    print("=" * 30)

    # Test different comma types
    test_strings = [
        "***********,***********",      # Regular comma, no spaces
        "***********, ***********",     # Regular comma with space
        "***********，***********",     # Unicode comma
        "***********; ***********",     # Semicolon
        "***********\t***********",     # Tab
    ]

    for test_str in test_strings:
        print(f"\nTesting: '{test_str}'")
        print(f"  Repr: {repr(test_str)}")
        print(f"  Bytes: {test_str.encode('utf-8')}")
        print(f"  Has ',': {',' in test_str}")
        print(f"  Has '，': {'，' in test_str}")
        print(f"  Has ';': {';' in test_str}")

        # Try splitting on different characters
        for sep in [',', '，', ';', '\t']:
            if sep in test_str:
                split_result = [v.strip() for v in test_str.split(sep) if v.strip()]
                print(f"  Split on '{sep}': {split_result}")

def test_your_exact_values():
    """Test with the exact values you mentioned."""

    print(f"\n🎯 Testing Your Exact Values")
    print("=" * 35)

    your_values = [
        "***********, ***********",
        "tcp\\22, tcp\\443"
    ]

    for value in your_values:
        print(f"\nTesting your value: '{value}'")
        print(f"  Type: {type(value)}")
        print(f"  Repr: {repr(value)}")
        print(f"  Bytes: {value.encode('utf-8')}")
        print(f"  Length: {len(value)}")

        # Character by character analysis
        print(f"  Characters:")
        for i, char in enumerate(value):
            print(f"    [{i}]: '{char}' (ord: {ord(char)}, hex: {hex(ord(char))})")

        # Test comma detection
        print(f"  Has comma: {',' in value}")
        if ',' in value:
            split_result = [v.strip() for v in value.split(',') if v.strip()]
            print(f"  Split result: {split_result}")
            print(f"  Split length: {len(split_result)}")
        else:
            print(f"  ❌ No comma detected!")

if __name__ == "__main__":
    print("🚨 Expansion Debugging Tool")
    print("=" * 60)
    
    # Test 1: Direct expansion function
    test_expansion_directly()
    
    # Test 2: Manual string splitting
    test_manual_split()
    
    # Test 3: Character inspection
    inspect_characters()

    # Test 4: Your exact values
    test_your_exact_values()

    # Test 5: CSV format check
    check_csv_format()

    # Test 6: Full CSV loading
    test_csv_loading()
    
    print(f"\n💡 What to look for:")
    print(f"   1. Does the expansion function receive comma-separated values?")
    print(f"   2. Are the values being split correctly?")
    print(f"   3. Is the expansion function being called at all?")
    print(f"   4. Are there any errors in the debug output?")
