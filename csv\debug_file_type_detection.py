#!/usr/bin/env python3
"""
Debug script to check why exe files are not being detected.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

def debug_file_type_detection(csv_file: str, skip_rows: int = 31):
    """Debug file type detection issues."""
    
    print("🔍 DEBUG: File Type Detection")
    print("=" * 50)
    
    try:
        # Load CSV data
        print(f"📁 Loading: {csv_file} (skip_rows={skip_rows})")
        flow_data = csv_to_dict_simple(csv_file, skip_rows=skip_rows)
        
        if not flow_data:
            print("❌ No data loaded!")
            print("Try different skip_rows values:")
            for test_skip in [0, 1, 30, 31, 32]:
                try:
                    test_data = csv_to_dict_simple(csv_file, skip_rows=test_skip)
                    if test_data:
                        sample = next(iter(test_data.values()))
                        print(f"  skip_rows={test_skip}: {len(test_data)} flows, headers: {list(sample.keys())[:3]}...")
                except:
                    print(f"  skip_rows={test_skip}: Failed")
            return
        
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Check column structure
        sample_flow = next(iter(flow_data.values()))
        print(f"\n📋 All columns detected:")
        for i, col in enumerate(sample_flow.keys()):
            print(f"  {i+1}. '{col}'")
        
        # Check specifically for File Type column
        file_type_variations = ["File Type", "file type", "FileType", "filetype", "File_Type"]
        file_type_column = None
        
        for variation in file_type_variations:
            if variation in sample_flow:
                file_type_column = variation
                print(f"✅ Found File Type column: '{variation}'")
                break
        
        if not file_type_column:
            print(f"❌ No File Type column found!")
            print(f"Available columns: {list(sample_flow.keys())}")
            return
        
        # Check File Type values
        print(f"\n📁 File Type values in all flows:")
        exe_found = False
        
        for flow_id, flow_data_item in flow_data.items():
            file_type_value = flow_data_item.get(file_type_column, "")
            print(f"  {flow_id}: '{file_type_value}'")
            
            # Check for exe specifically
            if file_type_value:
                if "exe" in file_type_value.lower():
                    exe_found = True
                    print(f"    ✅ EXE DETECTED in {flow_id}")
        
        if not exe_found:
            print(f"\n❌ No 'exe' found in any File Type values!")
            print(f"Check your CSV file to make sure it contains 'exe' in the File Type column")
            return
        
        # Test validation configuration
        print(f"\n🔧 Checking validation configuration...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        
        # Check if File Type is in unacceptable values
        if "File Type" in validator.unacceptable_values:
            print(f"✅ File Type rules found in unacceptable_values.json")
            file_type_rules = validator.unacceptable_values["File Type"]
            
            # Check if exe is in blocked list
            blocked_files = file_type_rules.get("blocked", [])
            if "exe" in [f.lower() for f in blocked_files]:
                print(f"✅ 'exe' is in blocked list: {blocked_files}")
            else:
                print(f"❌ 'exe' NOT in blocked list: {blocked_files}")
        else:
            print(f"❌ File Type rules NOT found in unacceptable_values.json")
        
        # Test manual validation
        print(f"\n🧪 Testing manual validation...")
        test_issues = validator._check_unacceptable_values("File Type", "exe")
        if test_issues:
            print(f"✅ Manual test: 'exe' produces {len(test_issues)} issues")
            for issue in test_issues:
                print(f"  - {issue}")
        else:
            print(f"❌ Manual test: 'exe' produces NO issues")
        
        # Run full validation
        print(f"\n🔍 Running full validation...")
        results = validator.validate_flow_data(flow_data)
        
        print(f"📊 Results:")
        print(f"  Total flows: {results['total_flows']}")
        print(f"  Flows with issues: {results['flows_with_issues']}")
        print(f"  Critical issues: {results['critical_issues']}")
        
        # Check each flow for File Type issues
        print(f"\n🔍 Checking each flow for File Type issues:")
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type_value = flow_data_item.get(file_type_column, "")
            issues = flow_result.get("issues", [])
            
            file_type_issues = [issue for issue in issues if issue.get("field") == "File Type"]
            
            print(f"  {flow_id}: File Type = '{file_type_value}'")
            if file_type_issues:
                for issue in file_type_issues:
                    print(f"    🚨 {issue.get('risk_level', 'unknown')}: {issue.get('message', 'no message')}")
            else:
                print(f"    ✅ No File Type issues")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main debug function."""
    
    # Test with your CSV file - modify this path
    csv_file = input("Enter CSV file path (or press Enter for 'Example_with_filetypes.csv'): ").strip()
    if not csv_file:
        csv_file = "Example_with_filetypes.csv"
    
    skip_rows = input("Enter number of rows to skip (or press Enter for 31): ").strip()
    if not skip_rows:
        skip_rows = 31
    else:
        skip_rows = int(skip_rows)
    
    debug_file_type_detection(csv_file, skip_rows)

if __name__ == "__main__":
    main()
