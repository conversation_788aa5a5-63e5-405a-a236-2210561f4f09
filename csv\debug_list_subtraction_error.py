#!/usr/bin/env python3
"""
Debug script to identify the source of the list subtraction error.
"""

from security_validator import SecurityValidator, csv_to_dict_simple, analyze_flow_expansion

def test_step_by_step():
    """Test each step to identify where the error occurs."""
    
    print("🔍 Step-by-Step Error Debugging")
    print("=" * 50)
    
    csv_file = "test_comma_expansion.csv"  # Change to your CSV file
    
    try:
        # Step 1: CSV Loading
        print("\n1️⃣ Testing CSV Loading...")
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        print(f"✅ CSV loaded: {len(flow_data)} flows")
        print(f"   Sample flow IDs: {list(flow_data.keys())[:3]}")
        
        # Step 2: Expansion Analysis
        print("\n2️⃣ Testing Expansion Analysis...")
        expansion_info = analyze_flow_expansion(flow_data)
        print(f"✅ Expansion analysis completed")
        print(f"   Total flows: {expansion_info['total_flows']}")
        print(f"   Original flows: {expansion_info['original_flows']}")
        print(f"   Expanded flows: {expansion_info['expanded_flows']}")
        
        # Step 3: Security Validation
        print("\n3️⃣ Testing Security Validation...")
        validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
        results = validator.validate_flow_data(flow_data)
        print(f"✅ Security validation completed")
        print(f"   Total flows: {results.get('total_flows', 0)}")
        print(f"   Critical issues: {results.get('critical_issues', 0)}")
        
        # Step 4: Report Generation
        print("\n4️⃣ Testing Report Generation...")
        from security_validator import generate_markdown_report
        report_file = generate_markdown_report(results, flow_data, csv_file, "debug_report.md")
        print(f"✅ Report generated: {report_file}")
        
        print(f"\n🎉 All steps completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error occurred: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

def test_expansion_analysis_only():
    """Test just the expansion analysis to see if that's where the error is."""
    
    print(f"\n🔍 Testing Expansion Analysis Only")
    print("=" * 40)
    
    # Create test data that should work
    test_flow_data = {
        "flow_1_a": {"Source IP": "********", "Destination IP": "********", "Port": "22", "Service": "ssh"},
        "flow_1_b": {"Source IP": "********", "Destination IP": "********", "Port": "22", "Service": "ssh"},
        "flow_2": {"Source IP": "********", "Destination IP": "********", "Port": "80", "Service": "http"}
    }
    
    print(f"Test data: {test_flow_data}")
    
    try:
        expansion_info = analyze_flow_expansion(test_flow_data)
        print(f"✅ Expansion analysis worked!")
        print(f"   Result: {expansion_info}")
    except Exception as e:
        print(f"❌ Error in expansion analysis: {e}")
        import traceback
        traceback.print_exc()

def test_with_your_csv():
    """Test with your actual CSV to see what flow data looks like."""
    
    print(f"\n📁 Testing with Your CSV")
    print("=" * 30)
    
    csv_file = "test_comma_expansion.csv"  # Change to your CSV file
    
    try:
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        
        print(f"Flow data loaded: {len(flow_data)} flows")
        print(f"Flow IDs: {list(flow_data.keys())}")
        
        # Check the structure of flow IDs
        for flow_id in list(flow_data.keys())[:5]:
            print(f"  {flow_id}: type={type(flow_id)}, value={repr(flow_id)}")
        
        # Check for any unusual data types
        for flow_id, flow_info in flow_data.items():
            if not isinstance(flow_id, str):
                print(f"⚠️  Non-string flow ID: {flow_id} (type: {type(flow_id)})")
            if not isinstance(flow_info, dict):
                print(f"⚠️  Non-dict flow info: {flow_info} (type: {type(flow_info)})")
                
        print(f"✅ Flow data structure looks good")
        
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        import traceback
        traceback.print_exc()

def test_validate_csv_file():
    """Test the complete validate_csv_file method."""
    
    print(f"\n🔄 Testing Complete validate_csv_file Method")
    print("=" * 50)
    
    csv_file = "test_comma_expansion.csv"  # Change to your CSV file
    
    try:
        validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
        results = validator.validate_csv_file(csv_file)
        
        print(f"✅ validate_csv_file completed!")
        print(f"   Success: {results.get('success', 'unknown')}")
        print(f"   Total flows: {results.get('total_flows', 0)}")
        print(f"   Critical issues: {results.get('critical_issues', 0)}")
        
    except Exception as e:
        print(f"❌ Error in validate_csv_file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚨 List Subtraction Error Debug Tool")
    print("=" * 60)
    
    # Test 1: Expansion analysis only
    test_expansion_analysis_only()
    
    # Test 2: Your CSV structure
    test_with_your_csv()
    
    # Test 3: Step by step
    test_step_by_step()
    
    # Test 4: Complete method
    test_validate_csv_file()
    
    print(f"\n💡 Look for the step where the error occurs!")
    print(f"   The error 'unsupported operand type(s) for -: 'list' and 'list'")
    print(f"   means somewhere in the code, two lists are being subtracted with -")
    print(f"   This is likely in the expansion analysis or report generation.")
