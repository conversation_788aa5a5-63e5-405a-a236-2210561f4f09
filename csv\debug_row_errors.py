"""
Debug script to identify exactly what's causing row validation errors.
"""

import json
import traceback
from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator


def debug_row_by_row():
    """Debug each row individually to find the exact error."""
    print("=== DEBUGGING ROW-BY-ROW VALIDATION ===")
    
    # Load CSV data
    try:
        flow_data = csv_to_dict_simple("Example.csv")
        print(f"✅ CSV loaded: {len(flow_data)} flows")
    except Exception as e:
        print(f"❌ CSV loading failed: {e}")
        return
    
    # Load configuration files
    try:
        with open("unacceptable_values.json", 'r') as f:
            unacceptable = json.load(f)
        print(f"✅ Unacceptable values loaded: {list(unacceptable.keys())}")
    except Exception as e:
        print(f"❌ Unacceptable values loading failed: {e}")
        unacceptable = {}
    
    try:
        with open("security_guidance.json", 'r') as f:
            guidance = json.load(f)
        print(f"✅ Guidance loaded: {list(guidance.keys())}")
    except Exception as e:
        print(f"❌ Guidance loading failed: {e}")
        guidance = {}
    
    # Initialize validator
    try:
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        print("✅ Validator initialized")
    except Exception as e:
        print(f"❌ Validator initialization failed: {e}")
        return
    
    print(f"\n=== PROCESSING {len(flow_data)} FLOWS ===")
    
    # Process each flow individually
    for i, (flow_id, flow_info) in enumerate(flow_data.items(), 1):
        print(f"\n--- Flow {i}: {flow_id} ---")
        print(f"Flow data: {flow_info}")
        
        try:
            # Test the individual flow validation
            flow_result = validator._validate_single_flow(flow_id, flow_info)
            print(f"✅ Flow validation successful")
            print(f"  Issues found: {len(flow_result.get('issues', []))}")
            print(f"  Overall risk: {flow_result.get('overall_risk', 'unknown')}")
            
            # Show any issues
            if flow_result.get('issues'):
                for issue in flow_result['issues']:
                    print(f"    - {issue.get('field', 'unknown')}: {issue.get('message', 'no message')}")
            
        except Exception as e:
            print(f"❌ Flow validation failed: {e}")
            print("Full traceback:")
            traceback.print_exc()
            
            # Try to identify the specific problem
            print("\nDiagnosing the error...")
            
            # Check if flow_info has expected structure
            if not isinstance(flow_info, dict):
                print(f"  Issue: flow_info is not a dict, it's {type(flow_info)}")
                continue
            
            # Check each field individually
            for field, value in flow_info.items():
                try:
                    print(f"  Checking field '{field}' = '{value}'")
                    
                    # Test unacceptable values check
                    if field in unacceptable:
                        field_rules = unacceptable[field]
                        print(f"    Found rules for {field}: {list(field_rules.keys())}")
                        
                        for category, values in field_rules.items():
                            if isinstance(values, list) and value.lower() in [v.lower() for v in values]:
                                print(f"    ⚠️ {field} '{value}' matches {category}")
                    
                    # Test guidance check
                    if field.lower() == "service" and "service_guidance" in guidance:
                        service_guidance = guidance["service_guidance"].get(value.lower())
                        if service_guidance:
                            print(f"    Found guidance for service '{value}'")
                    
                except Exception as field_error:
                    print(f"    ❌ Error processing field '{field}': {field_error}")
    
    print(f"\n=== TESTING FULL VALIDATION ===")
    
    # Test the full validation
    try:
        results = validator.validate_flow_data(flow_data)
        print(f"✅ Full validation completed")
        print(f"  Total flows: {results.get('total_flows', 'missing')}")
        print(f"  Flows with issues: {results.get('flows_with_issues', 'missing')}")
        print(f"  Errors: {len(results.get('errors', []))}")
        
        if results.get('errors'):
            print("  Error details:")
            for error in results['errors']:
                print(f"    - {error}")
        
    except Exception as e:
        print(f"❌ Full validation failed: {e}")
        traceback.print_exc()


def test_minimal_flow():
    """Test with minimal flow data to isolate the issue."""
    print(f"\n=== TESTING MINIMAL FLOW ===")
    
    minimal_flow = {
        "flow_test": {
            "Service": "https",
            "Port": "443",
            "Action": "Allow"
        }
    }
    
    try:
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(minimal_flow)
        print(f"✅ Minimal flow test passed")
        print(f"  Flows with issues: {results.get('flows_with_issues', 'missing')}")
    except Exception as e:
        print(f"❌ Minimal flow test failed: {e}")
        traceback.print_exc()


def main():
    """Run all debugging tests."""
    print("CSV ROW VALIDATION ERROR DEBUGGER")
    print("=" * 50)
    
    debug_row_by_row()
    test_minimal_flow()
    
    print(f"\n=== DEBUGGING COMPLETE ===")
    print("Check the output above to see exactly where the validation errors occur.")


if __name__ == "__main__":
    main()
