#!/usr/bin/env python3
"""Debug script to understand why all rows are being skipped."""

import csv

def debug_detailed_parsing(csv_file_path):
    """Debug CSV parsing in detail to see why rows are being skipped."""
    print(f"🔍 Detailed debugging of CSV file: {csv_file_path}")
    
    # First, let's see the raw file content
    print(f"\n📄 Raw file content:")
    with open(csv_file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines[:10], 1):  # Show first 10 lines
            print(f"  Line {i}: {repr(line)}")
    
    print(f"\n🔍 CSV parsing analysis:")
    
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        csv_reader = csv.reader(csvfile)
        
        flow_rows_found = 0
        header_row_index = -1
        clean_headers = []
        
        # First pass - find headers
        for row_index, row in enumerate(csv_reader):
            print(f"\nRow {row_index + 1}: {row}")
            print(f"  Length: {len(row)}")
            
            if row and len(row) > 0:
                first_col = str(row[0]).strip().lower() if row[0] else ""
                print(f"  First column: '{first_col}'")
                print(f"  Starts with 'flow': {first_col.startswith('flow')}")
                
                if first_col.startswith('flow'):
                    if header_row_index == -1:
                        # This should be the first Flow row, previous row should be headers
                        if row_index > 0:
                            header_row_index = row_index - 1
                            print(f"  *** Found first Flow row! Header row should be at index {header_row_index}")
                        else:
                            header_row_index = 0
                            print(f"  *** Flow row is first row, using it as headers")
                    
                    flow_rows_found += 1
                    print(f"  *** This is Flow row #{flow_rows_found}")
        
        print(f"\n📊 Summary from first pass:")
        print(f"  Flow rows found: {flow_rows_found}")
        print(f"  Header row index: {header_row_index}")
    
    # Second pass - simulate the actual processing
    print(f"\n🔄 Second pass - simulating actual processing:")
    
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        csv_reader = csv.reader(csvfile)
        
        # Get to the header row
        for i in range(header_row_index + 1):
            header_row = next(csv_reader)
        
        clean_headers = [header.strip() for header in header_row[1:] if header.strip()]
        print(f"  Clean headers: {clean_headers}")
        
        # Now process Flow rows
        flow_counter = 1
        for row in csv_reader:
            if row and len(row) > 0:
                first_col = str(row[0]).strip().lower() if row[0] else ""
                if first_col.startswith('flow'):
                    print(f"\n  Processing Flow row {flow_counter}: {row}")
                    
                    # Simulate the data checking logic
                    row_dict = {}
                    has_data = False
                    
                    for i, header in enumerate(clean_headers):
                        if i + 1 < len(row):
                            raw_value = row[i + 1] if i + 1 < len(row) else ""
                            cell_value = str(raw_value).strip() if raw_value is not None else ""
                            row_dict[header] = cell_value
                            
                            print(f"    Header '{header}' (index {i+1}): raw='{raw_value}', processed='{cell_value}'")
                            print(f"      raw_value is not None: {raw_value is not None}")
                            print(f"      str(raw_value).strip(): '{str(raw_value).strip()}'")
                            print(f"      bool(str(raw_value).strip()): {bool(str(raw_value).strip())}")
                            
                            if raw_value is not None and str(raw_value).strip():
                                has_data = True
                                print(f"      *** This cell has data! ***")
                    
                    print(f"    Final has_data: {has_data}")
                    print(f"    Row dict: {row_dict}")
                    
                    if has_data:
                        print(f"    ✅ Would INCLUDE this row")
                    else:
                        print(f"    ❌ Would SKIP this row")
                    
                    flow_counter += 1

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        debug_detailed_parsing(sys.argv[1])
    else:
        print("Usage: python debug_row_skipping.py <csv_file>")
        print("Available test files:")
        print("  - Example.csv")
        print("  - Example_with_31_header_rows.csv")
        print("  - test_empty_rows.csv")
        print("  - test_partial_data.csv")
