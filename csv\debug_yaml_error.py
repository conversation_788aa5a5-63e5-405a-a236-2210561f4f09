#!/usr/bin/env python3
"""
Debug script to identify the source of the YAML export error.
"""

from security_validator import csv_to_dict_simple, SecurityValidator

def debug_flow_data_structure():
    """Debug the flow data structure to see what's causing the error."""
    
    print("🔍 Debugging Flow Data Structure")
    print("=" * 50)
    
    csv_file = "test_comma_expansion.csv"  # Change to your CSV file
    
    try:
        # Step 1: Load CSV data
        print(f"\n1️⃣ Loading CSV data...")
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        
        print(f"   Type of flow_data: {type(flow_data)}")
        print(f"   Length: {len(flow_data) if hasattr(flow_data, '__len__') else 'No length'}")
        
        if isinstance(flow_data, dict):
            print(f"   ✅ flow_data is a dictionary")
            
            # Check first few items
            print(f"\n   📋 First few items:")
            for i, (key, value) in enumerate(list(flow_data.items())[:5]):
                print(f"      {i+1}. Key: {repr(key)} (type: {type(key)})")
                print(f"         Value: {repr(value)} (type: {type(value)})")
                
                if isinstance(value, dict):
                    print(f"         ✅ Value is dict with keys: {list(value.keys())}")
                    # Check a few field values
                    for field_name, field_value in list(value.items())[:3]:
                        print(f"            {field_name}: {repr(field_value)} (type: {type(field_value)})")
                else:
                    print(f"         ❌ Value is not dict!")
                print()
        else:
            print(f"   ❌ flow_data is not a dictionary: {type(flow_data)}")
            print(f"   Content: {flow_data}")
            return
        
        # Step 2: Test the problematic operation
        print(f"\n2️⃣ Testing problematic operations...")
        
        # Test getting Source IP from first flow
        if flow_data:
            first_flow_id, first_flow_data = list(flow_data.items())[0]
            print(f"   Testing first flow: {first_flow_id}")
            print(f"   Flow data type: {type(first_flow_data)}")
            
            if isinstance(first_flow_data, dict):
                print(f"   Available fields: {list(first_flow_data.keys())}")
                
                # Test the .get() operation that's failing
                try:
                    source_ip = first_flow_data.get('Source IP', '')
                    print(f"   ✅ Source IP: '{source_ip}'")
                except Exception as e:
                    print(f"   ❌ Error getting Source IP: {e}")
                    print(f"      first_flow_data: {first_flow_data}")
                
                # Test other fields
                for field in ['Destination IP', 'Port', 'Service', 'Action', 'File Type']:
                    try:
                        value = first_flow_data.get(field, '')
                        print(f"   ✅ {field}: '{value}'")
                    except Exception as e:
                        print(f"   ❌ Error getting {field}: {e}")
            else:
                print(f"   ❌ First flow data is not dict: {type(first_flow_data)}")
                print(f"      Content: {first_flow_data}")
        
    except Exception as e:
        print(f"❌ Error in debug: {e}")
        import traceback
        traceback.print_exc()

def debug_validate_csv_file():
    """Debug the validate_csv_file process to see where data gets corrupted."""
    
    print(f"\n🔧 Debugging validate_csv_file Process")
    print("=" * 50)
    
    csv_file = "test_comma_expansion.csv"
    
    try:
        # Step 1: Test CSV loading directly
        print(f"\n1️⃣ Testing csv_to_dict_simple directly...")
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        print(f"   Direct load result: {type(flow_data)} with {len(flow_data)} items")
        
        # Step 2: Test validate_csv_structure
        print(f"\n2️⃣ Testing validate_csv_structure...")
        from security_validator import validate_csv_structure
        validated_data, validation_issues = validate_csv_structure(flow_data)
        print(f"   Validation result: {type(validated_data)} with {len(validated_data)} items")
        print(f"   Issues: {validation_issues}")
        
        # Check if validated_data is different from flow_data
        if validated_data is flow_data:
            print(f"   ✅ validated_data is same object as flow_data")
        else:
            print(f"   ⚠️  validated_data is different object")
            
        # Step 3: Check structure of validated_data
        print(f"\n3️⃣ Checking validated_data structure...")
        if isinstance(validated_data, dict):
            for i, (key, value) in enumerate(list(validated_data.items())[:3]):
                print(f"   Item {i+1}: {repr(key)} -> {type(value)}")
                if isinstance(value, dict):
                    print(f"      Fields: {list(value.keys())}")
                else:
                    print(f"      ❌ Not a dict: {value}")
        
    except Exception as e:
        print(f"❌ Error in validate_csv_file debug: {e}")
        import traceback
        traceback.print_exc()

def test_yaml_export_directly():
    """Test YAML export with known good data."""
    
    print(f"\n📤 Testing YAML Export with Known Good Data")
    print("=" * 55)
    
    # Create test data that should definitely work
    test_data = {
        "flow_1": {
            "Source IP": "***********",
            "Destination IP": "***********0",
            "Port": "443",
            "Service": "https",
            "Action": "Allow",
            "File Type": "pdf"
        },
        "flow_2": {
            "Source IP": "***********",
            "Destination IP": "************",
            "Port": "22",
            "Service": "ssh", 
            "Action": "Allow",
            "File Type": "exe"
        }
    }
    
    print(f"Test data: {test_data}")
    
    try:
        from security_validator import export_original_flows_to_yaml
        
        yaml_file = export_original_flows_to_yaml(
            test_data,
            "debug_test.yaml",
            include_expanded=False
        )
        
        print(f"✅ YAML export successful: {yaml_file}")
        
        # Show content
        with open(yaml_file, 'r') as f:
            content = f.read()
            print(f"\nGenerated YAML:")
            print(content[:500] + "..." if len(content) > 500 else content)
            
    except Exception as e:
        print(f"❌ Error in YAML export: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚨 YAML Export Error Debug Tool")
    print("=" * 60)
    
    # Test 1: Flow data structure
    debug_flow_data_structure()
    
    # Test 2: validate_csv_file process
    debug_validate_csv_file()
    
    # Test 3: YAML export with good data
    test_yaml_export_directly()
    
    print(f"\n💡 Look for:")
    print(f"   1. Where flow_data stops being a proper dict")
    print(f"   2. Where integers appear instead of strings")
    print(f"   3. Where the .get() method fails")
    print(f"   4. Any data corruption in the validation process")
