#!/usr/bin/env python3
"""
Demonstration of Excel validation concepts.
Shows how validation rules would work in practice.
"""

import re

class ExcelValidationDemo:
    """Simulate Excel validation behavior."""
    
    def __init__(self):
        self.valid_services = ["http", "https", "ssh", "ftp", "telnet", "smtp", "dns", "dhcp"]
        self.valid_actions = ["Allow", "Deny", "Block"]
    
    def validate_ip_address(self, ip_value):
        """Simulate Excel IP validation formula."""
        if not ip_value:
            return False, "IP address is required"
        
        # Check basic format
        if len(ip_value) < 7 or len(ip_value) > 15:
            return False, "IP address length must be 7-15 characters"
        
        # Check for exactly 3 dots
        if ip_value.count('.') != 3:
            return False, "IP address must have exactly 3 dots"
        
        # Validate each octet
        try:
            parts = ip_value.split('.')
            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    return False, f"IP octet '{part}' must be 0-255"
        except ValueError:
            return False, "IP address must contain only numbers and dots"
        
        return True, "Valid IP address"
    
    def validate_port(self, port_value):
        """Simulate Excel port validation."""
        if not port_value:
            return False, "Port is required"
        
        # Handle comma-separated ports
        if ',' in port_value:
            ports = [p.strip() for p in port_value.split(',')]
            for port in ports:
                valid, msg = self._validate_single_port(port)
                if not valid:
                    return False, f"In comma-separated list: {msg}"
            return True, f"Valid comma-separated ports: {port_value}"
        else:
            return self._validate_single_port(port_value)
    
    def _validate_single_port(self, port):
        """Validate a single port number."""
        try:
            port_num = int(port)
            if port_num < 1 or port_num > 65535:
                return False, f"Port '{port}' must be between 1 and 65535"
            return True, f"Valid port: {port}"
        except ValueError:
            return False, f"Port '{port}' must be a number"
    
    def validate_service(self, service_value):
        """Simulate Excel service validation."""
        if not service_value:
            return False, "Service is required"
        
        # Handle comma-separated services
        if ',' in service_value:
            services = [s.strip().lower() for s in service_value.split(',')]
            for service in services:
                if service not in self.valid_services:
                    return False, f"Service '{service}' not in allowed list: {', '.join(self.valid_services)}"
            return True, f"Valid comma-separated services: {service_value}"
        else:
            if service_value.lower() not in self.valid_services:
                return False, f"Service '{service_value}' not in allowed list: {', '.join(self.valid_services)}"
            return True, f"Valid service: {service_value}"
    
    def validate_action(self, action_value):
        """Simulate Excel action validation."""
        if not action_value:
            return False, "Action is required"
        
        if action_value not in self.valid_actions:
            return False, f"Action '{action_value}' must be one of: {', '.join(self.valid_actions)}"
        
        return True, f"Valid action: {action_value}"
    
    def validate_file_type(self, file_type_value):
        """Simulate Excel file type validation."""
        # File type is optional
        if not file_type_value:
            return True, "File type is optional"
        
        # Handle comma-separated file types
        if ',' in file_type_value:
            file_types = [ft.strip() for ft in file_type_value.split(',')]
            for file_type in file_types:
                valid, msg = self._validate_single_file_type(file_type)
                if not valid:
                    return False, f"In comma-separated list: {msg}"
            return True, f"Valid comma-separated file types: {file_type_value}"
        else:
            return self._validate_single_file_type(file_type_value)
    
    def _validate_single_file_type(self, file_type):
        """Validate a single file type."""
        # Must be alphanumeric only
        if not re.match(r'^[a-zA-Z0-9]+$', file_type):
            return False, f"File type '{file_type}' must be alphanumeric only"
        
        return True, f"Valid file type: {file_type}"
    
    def validate_row(self, row_data):
        """Validate an entire row of data."""
        results = {}
        overall_valid = True
        
        # Expected columns
        columns = ["Source IP", "Destination IP", "Port", "Service", "Action", "File Type"]
        
        for i, column in enumerate(columns):
            if i < len(row_data):
                value = row_data[i]
                
                if column == "Source IP" or column == "Destination IP":
                    valid, msg = self.validate_ip_address(value)
                elif column == "Port":
                    valid, msg = self.validate_port(value)
                elif column == "Service":
                    valid, msg = self.validate_service(value)
                elif column == "Action":
                    valid, msg = self.validate_action(value)
                elif column == "File Type":
                    valid, msg = self.validate_file_type(value)
                else:
                    valid, msg = True, "No validation rule"
                
                results[column] = {"valid": valid, "message": msg, "value": value}
                if not valid:
                    overall_valid = False
            else:
                results[column] = {"valid": False, "message": "Missing value", "value": ""}
                overall_valid = False
        
        return overall_valid, results

def demo_validation_scenarios():
    """Demonstrate various validation scenarios."""
    
    print("🧪 Excel Validation Demo")
    print("=" * 60)
    
    validator = ExcelValidationDemo()
    
    # Test scenarios
    test_cases = [
        {
            "name": "Valid Row",
            "data": ["***********", "************", "443", "https", "Allow", "pdf"]
        },
        {
            "name": "Valid with Comma-Separated Values",
            "data": ["***********", "***********0", "22,80,443", "ssh,http,https", "Allow", "exe,dll"]
        },
        {
            "name": "Invalid IP Address",
            "data": ["invalid_ip", "************", "443", "https", "Allow", "pdf"]
        },
        {
            "name": "Invalid Port",
            "data": ["***********", "************", "99999", "https", "Allow", "pdf"]
        },
        {
            "name": "Invalid Service",
            "data": ["***********", "************", "443", "invalid_service", "Allow", "pdf"]
        },
        {
            "name": "Invalid Action",
            "data": ["***********", "************", "443", "https", "Maybe", "pdf"]
        },
        {
            "name": "Invalid File Type",
            "data": ["***********", "************", "443", "https", "Allow", "bad@file"]
        },
        {
            "name": "Missing Required Fields",
            "data": ["", "", "", "", "", ""]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"Data: {test_case['data']}")
        
        overall_valid, results = validator.validate_row(test_case['data'])
        
        print(f"Overall Valid: {'✅' if overall_valid else '❌'}")
        
        for column, result in results.items():
            status = "✅" if result['valid'] else "❌"
            print(f"  {status} {column}: {result['message']}")
    
    print(f"\n💡 In Excel, this validation would:")
    print(f"   • Highlight invalid cells in red")
    print(f"   • Show error messages as tooltips")
    print(f"   • Prevent saving until all errors are fixed")
    print(f"   • Provide dropdown menus for constrained fields")

def demo_excel_features():
    """Demonstrate Excel-specific validation features."""
    
    print(f"\n🔧 Excel Validation Features Demo")
    print("=" * 50)
    
    print(f"\n📋 Built-in Data Validation:")
    print(f"   • Dropdown lists for Service and Action fields")
    print(f"   • Custom formulas for IP address validation")
    print(f"   • Range validation for port numbers")
    print(f"   • Input messages and error alerts")
    
    print(f"\n🔄 VBA Macro Features:")
    print(f"   • Real-time validation as users type")
    print(f"   • Automatic error highlighting")
    print(f"   • Save prevention with validation errors")
    print(f"   • Custom error messages and tooltips")
    
    print(f"\n🎨 Visual Feedback:")
    print(f"   • Red background for invalid cells")
    print(f"   • Error comments with detailed explanations")
    print(f"   • Conditional formatting for warnings")
    print(f"   • Progress indicators for large datasets")
    
    print(f"\n👥 User Experience:")
    print(f"   • Immediate feedback prevents errors")
    print(f"   • Clear guidance on expected formats")
    print(f"   • No need to remember validation rules")
    print(f"   • Consistent data quality across entries")

def show_implementation_steps():
    """Show step-by-step implementation."""
    
    print(f"\n📝 Implementation Steps")
    print("=" * 40)
    
    steps = [
        "1. Create Excel template with headers",
        "2. Apply data validation rules to each column",
        "3. Set up dropdown menus for constrained fields",
        "4. Add VBA macros for real-time validation",
        "5. Test validation with sample data",
        "6. Create user instructions and training",
        "7. Deploy template to users",
        "8. Monitor and update validation rules as needed"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n📁 Files Provided:")
    print(f"   • excel_validation_macros.vba - Complete VBA code")
    print(f"   • excel_validation_guide.md - Detailed setup instructions")
    print(f"   • create_excel_template.py - Automated template creation")
    print(f"   • This demo script - Validation behavior simulation")

if __name__ == "__main__":
    demo_validation_scenarios()
    demo_excel_features()
    show_implementation_steps()
    
    print(f"\n🎯 Next Steps:")
    print(f"1. Review excel_validation_guide.md for detailed setup")
    print(f"2. Use excel_validation_macros.vba in your Excel file")
    print(f"3. Test validation with your specific data requirements")
    print(f"4. Customize validation rules as needed")
    print(f"5. Deploy to users with training materials")
