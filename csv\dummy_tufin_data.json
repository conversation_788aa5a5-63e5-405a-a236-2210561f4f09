{"devices": [{"id": 1001, "name": "DMZ-FW-01", "type": "firewall", "vendor": "Checkpoint", "model": "R81.20", "ip_address": "*********", "owner": "<EMAIL>", "administrator": "<EMAIL>", "location": "DMZ", "status": "active", "domain_id": 1}, {"id": 1002, "name": "WEB-FW-02", "type": "firewall", "vendor": "Fortinet", "model": "FortiGate-600E", "ip_address": "*********", "owner": "<EMAIL>", "administrator": "<EMAIL>", "location": "Web DMZ", "status": "active", "domain_id": 1}, {"id": 1003, "name": "DB-FW-03", "type": "firewall", "vendor": "Palo Alto", "model": "PA-5220", "ip_address": "*********", "owner": "<EMAIL>", "administrator": "<EMAIL>", "location": "Database Zone", "status": "active", "domain_id": 2}, {"id": 1004, "name": "CORE-FW-04", "type": "firewall", "vendor": "Cisco", "model": "ASA-5555", "ip_address": "*********", "owner": "<EMAIL>", "administrator": "<EMAIL>", "location": "Core Network", "status": "active", "domain_id": 1}, {"id": 1005, "name": "MAIL-FW-05", "type": "firewall", "vendor": "Checkpoint", "model": "R81.10", "ip_address": "*********", "owner": "<EMAIL>", "administrator": "<EMAIL>", "location": "Mail Zone", "status": "active", "domain_id": 2}], "rules": {"1001": [{"id": "R1001-001", "number": 1, "name": "DMZ Web Server Access", "enabled": true, "source": "Any", "destination": "DMZ_Web_Servers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "john.doe", "created_date": "2023-01-15", "last_modified": "2024-01-10", "expiration_date": "2024-02-15", "last_certified": "2023-02-15", "certification_expiry": "2024-02-15", "description": "Allow web traffic to DMZ web servers. Contact: <EMAIL> for changes.", "comments": "Annual recertification required", "risk_level": "medium"}, {"id": "R1001-002", "number": 2, "name": "DMZ SSH Management", "enabled": true, "source": "Management_Network", "destination": "DMZ_Servers", "service": "SSH", "action": "Allow", "owner": "<EMAIL>", "created_by": "admin", "created_date": "2023-03-01", "last_modified": "2024-01-05", "expiration_date": "2024-03-01", "last_certified": "2023-03-01", "certification_expiry": "2024-03-01", "description": "SSH access for DMZ server management", "comments": "Critical for operations - owner: <EMAIL>", "risk_level": "high"}, {"id": "R1001-003", "number": 3, "name": "Legacy FTP Access", "enabled": true, "source": "Internal_Network", "destination": "DMZ_FTP_Server", "service": "FTP", "action": "Allow", "owner": "", "created_by": "legacy.user", "created_date": "2022-06-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-30", "last_certified": "", "certification_expiry": "", "description": "Legacy FTP access - needs review", "comments": "No clear owner identified", "risk_level": "critical"}], "1002": [{"id": "R1002-001", "number": 1, "name": "WEB Load Balancer Access", "enabled": true, "source": "Internet", "destination": "Web_Load_Balancers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "web.admin", "created_date": "2023-05-01", "last_modified": "2024-01-12", "expiration_date": "2024-02-20", "last_certified": "2023-05-01", "certification_expiry": "2024-05-01", "description": "Public web access through load balancers", "comments": "Business critical rule", "risk_level": "medium"}, {"id": "R1002-002", "number": 2, "name": "WEB Database Connection", "enabled": true, "source": "Web_Servers", "destination": "Database_Servers", "service": "MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "developer1", "created_date": "2023-04-15", "last_modified": "2024-01-08", "expiration_date": "2024-02-10", "last_certified": "2023-04-15", "certification_expiry": "2024-04-15", "description": "Web application database connectivity", "comments": "Application team responsible", "risk_level": "high"}, {"id": "R1002-003", "number": 3, "name": "WEB Monitoring Access", "enabled": true, "source": "Monitoring_Servers", "destination": "Web_Infrastructure", "service": "SNMP,HTTP", "action": "Allow", "owner": "<EMAIL>", "created_by": "monitor.admin", "created_date": "2023-07-01", "last_modified": "2023-12-15", "expiration_date": "2024-01-25", "last_certified": "2023-07-01", "certification_expiry": "2024-07-01", "description": "Infrastructure monitoring access", "comments": "Ops team maintains this rule", "risk_level": "low"}], "1003": [{"id": "R1003-001", "number": 1, "name": "Database Replication", "enabled": true, "source": "Primary_DB_Servers", "destination": "Secondary_DB_Servers", "service": "MySQL_Replication", "action": "Allow", "owner": "<EMAIL>", "created_by": "dba1", "created_date": "2023-02-01", "last_modified": "2024-01-03", "expiration_date": "2024-02-28", "last_certified": "2023-02-01", "certification_expiry": "2024-02-01", "description": "Database replication between primary and secondary servers", "comments": "Critical for DR - DBA team owns this", "risk_level": "high"}, {"id": "R1003-002", "number": 2, "name": "Database Backup Access", "enabled": true, "source": "Backup_Servers", "destination": "Database_Servers", "service": "Custom_Backup_Port", "action": "Allow", "owner": "<EMAIL>", "created_by": "backup.admin", "created_date": "2023-01-10", "last_modified": "2023-11-20", "expiration_date": "2024-01-31", "last_certified": "2023-01-10", "certification_expiry": "2024-01-10", "description": "Backup system access to databases", "comments": "Backup team responsible", "risk_level": "medium"}, {"id": "R1003-003", "number": 3, "name": "Database Admin Access", "enabled": true, "source": "DBA_Workstations", "destination": "All_Database_Servers", "service": "SSH,RDP,MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "senior.dba", "created_date": "2023-03-15", "last_modified": "2024-01-01", "expiration_date": "2024-03-15", "last_certified": "2023-03-15", "certification_expiry": "2024-03-15", "description": "DBA administrative access to all database servers", "comments": "Senior DBA approval required for changes", "risk_level": "critical"}], "1004": [{"id": "R1004-001", "number": 1, "name": "Core Network Routing", "enabled": true, "source": "Internal_Networks", "destination": "Core_Infrastructure", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "network.admin", "created_date": "2023-01-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-28", "last_certified": "2023-01-01", "certification_expiry": "2024-01-01", "description": "Core network routing and connectivity", "comments": "Network team maintains - critical infrastructure", "risk_level": "high"}, {"id": "R1004-002", "number": 2, "name": "VPN Access Rule", "enabled": true, "source": "VPN_Clients", "destination": "Internal_Resources", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "vpn.admin", "created_date": "2023-06-01", "last_modified": "2024-01-07", "expiration_date": "2024-02-05", "last_certified": "2023-06-01", "certification_expiry": "2024-06-01", "description": "VPN user access to internal resources", "comments": "VPN team manages user access", "risk_level": "medium"}, {"id": "R1004-003", "number": 3, "name": "Guest Network Access", "enabled": true, "source": "Guest_Network", "destination": "Internet", "service": "HTTP,HTTPS", "action": "Allow", "owner": "", "created_by": "temp.admin", "created_date": "2023-08-01", "last_modified": "2023-08-01", "expiration_date": "2024-02-01", "last_certified": "", "certification_expiry": "", "description": "Guest network internet access", "comments": "Temporary rule - no clear owner", "risk_level": "medium"}], "1005": [{"id": "R1005-001", "number": 1, "name": "SMTP Inbound Mail", "enabled": true, "source": "Internet", "destination": "Mail_Servers", "service": "SMTP,SMTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "mail.admin", "created_date": "2023-04-01", "last_modified": "2024-01-11", "expiration_date": "2024-02-12", "last_certified": "2023-04-01", "certification_expiry": "2024-04-01", "description": "Inbound email delivery", "comments": "Mail team - business critical", "risk_level": "high"}, {"id": "R1005-002", "number": 2, "name": "IMAP/POP3 Access", "enabled": true, "source": "Internal_Users", "destination": "Mail_Servers", "service": "IMAP,IMAPS,POP3,POP3S", "action": "Allow", "owner": "<EMAIL>", "created_by": "msg.admin", "created_date": "2023-05-15", "last_modified": "2023-12-20", "expiration_date": "2024-01-20", "last_certified": "2023-05-15", "certification_expiry": "2024-05-15", "description": "User email access via IMAP/POP3", "comments": "Messaging team responsibility", "risk_level": "medium"}, {"id": "R1005-003", "number": 3, "name": "Mail Relay Legacy", "enabled": true, "source": "Legacy_Applications", "destination": "Mail_Relay", "service": "SMTP", "action": "Allow", "owner": "", "created_by": "unknown", "created_date": "2022-01-01", "last_modified": "2022-01-01", "expiration_date": "2024-01-22", "last_certified": "", "certification_expiry": "", "description": "Legacy application mail relay - created by unknown", "comments": "Old rule with no clear ownership", "risk_level": "high"}]}, "users": [{"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Web Development Team", "department": "IT - Web Services"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "DMZ Administrator", "department": "IT - Network Security"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Database Administration Team", "department": "IT - Database Services"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Network Operations Team", "department": "IT - Network Operations"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Mail Administrator", "department": "IT - Messaging Services"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Security Team", "department": "IT - Information Security"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Application Development Team", "department": "IT - Application Development"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Backup and Recovery Team", "department": "IT - Infrastructure"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "Infrastructure Monitoring Team", "department": "IT - Operations"}, {"username": "<EMAIL>", "active": true, "can_receive_assignments": true, "full_name": "VPN Administrator", "department": "IT - Network Security"}]}