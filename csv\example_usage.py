#!/usr/bin/env python3
"""
Example usage of the consolidated SecurityValidator.

This shows how to use the SecurityValidator to validate CSV files
and get security analysis results in a single function call.
"""

from security_validator import SecurityValidator

def main():
    """Example of using the consolidated security validator."""
    
    # Initialize the validator with your reference files
    validator = SecurityValidator(
        unacceptable_values_file="unacceptable_values.json",
        guidance_file="security_guidance.json"
    )
    
    # Validate a CSV file - this single call does everything:
    # 1. Loads and validates CSV structure
    # 2. Performs security analysis
    # 3. Generates detailed reports
    # 4. Displays formatted results
    csv_file = "Example.csv"
    results = validator.validate_csv_file(csv_file)
    
    # The results dictionary contains all the information:
    if results.get("success"):
        print(f"\n🎉 Validation successful!")
        print(f"   📊 Analyzed {results['total_flows']} flows")
        print(f"   🔴 Critical issues: {results['critical_issues']}")
        print(f"   🟠 High risk issues: {results['high_risk_issues']}")
        print(f"   🟡 Medium risk issues: {results['medium_risk_issues']}")
        
        # Access individual flow results if needed
        for flow_id, flow_result in results["flow_results"].items():
            flow_data = flow_result.get("flow_data", {})
            issues = flow_result.get("issues", [])
            print(f"   Flow {flow_id}: {len(issues)} issues")
    else:
        print(f"❌ Validation failed: {results.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
