' Network Flow Data Validation Macros
' Place this code in the Excel workbook's VBA editor
' These macros enforce data validation rules in real-time

Option Explicit

' Global variables for validation
Const REQUIRED_COLUMNS As String = "Source IP,Destination IP,Port,Service,Action"
Const VALID_SERVICES As String = "http,https,ssh,ftp,telnet,smtp,dns,dhcp"
Const VALID_ACTIONS As String = "Allow,Deny,Block"

' Event handler for worksheet changes
Private Sub Worksheet_Change(ByVal Target As Range)
    ' Disable events to prevent infinite loops
    Application.EnableEvents = False
    
    ' Only validate data rows (not headers)
    If Target.Row > 1 Then
        Call ValidateChangedCell(Target)
    End If
    
    ' Re-enable events
    Application.EnableEvents = True
End Sub

' Event handler for before saving
Private Sub Workbook_BeforeSave(ByVal SaveAsUI As Boolean, Cancel As Boolean)
    Dim validationResult As Boolean
    validationResult = ValidateAllData()
    
    If Not validationResult Then
        MsgBox "Cannot save: Data validation errors found. Please fix all highlighted errors before saving.", vbCritical, "Validation Failed"
        Cancel = True
    End If
End Sub

' Validate a single cell when it's changed
Sub ValidateChangedCell(ByVal Target As Range)
    Dim columnName As String
    Dim cellValue As String
    Dim isValid As Boolean
    
    ' Get column header
    columnName = Cells(1, Target.Column).Value
    cellValue = Target.Value
    
    ' Clear previous formatting
    Target.Interior.Color = xlNone
    Target.Font.Color = vbBlack
    
    ' Skip validation for empty cells in optional columns
    If cellValue = "" And Not IsRequiredColumn(columnName) Then
        Exit Sub
    End If
    
    ' Validate based on column type
    Select Case columnName
        Case "Source IP", "Destination IP"
            isValid = ValidateIPAddress(cellValue)
            If Not isValid Then
                HighlightError Target, "Invalid IP address format"
            End If
            
        Case "Port"
            isValid = ValidatePort(cellValue)
            If Not isValid Then
                HighlightError Target, "Invalid port number (1-65535) or comma-separated ports"
            End If
            
        Case "Service"
            isValid = ValidateService(cellValue)
            If Not isValid Then
                HighlightError Target, "Invalid service. Allowed: " & VALID_SERVICES
            End If
            
        Case "Action"
            isValid = ValidateAction(cellValue)
            If Not isValid Then
                HighlightError Target, "Invalid action. Allowed: " & VALID_ACTIONS
            End If
            
        Case "File Type"
            isValid = ValidateFileType(cellValue)
            If Not isValid Then
                HighlightError Target, "Invalid file type. Use alphanumeric characters only"
            End If
    End Select
End Sub

' Validate IP address format
Function ValidateIPAddress(ByVal ipAddress As String) As Boolean
    Dim parts() As String
    Dim i As Integer
    Dim part As Integer
    
    ' Check if empty (not allowed for IP fields)
    If ipAddress = "" Then
        ValidateIPAddress = False
        Exit Function
    End If
    
    ' Split by dots
    parts = Split(ipAddress, ".")
    
    ' Must have exactly 4 parts
    If UBound(parts) <> 3 Then
        ValidateIPAddress = False
        Exit Function
    End If
    
    ' Validate each part
    For i = 0 To 3
        If Not IsNumeric(parts(i)) Then
            ValidateIPAddress = False
            Exit Function
        End If
        
        part = CInt(parts(i))
        If part < 0 Or part > 255 Then
            ValidateIPAddress = False
            Exit Function
        End If
    Next i
    
    ValidateIPAddress = True
End Function

' Validate port number(s)
Function ValidatePort(ByVal portValue As String) As Boolean
    Dim ports() As String
    Dim i As Integer
    Dim port As Integer
    
    ' Check if empty (not allowed)
    If portValue = "" Then
        ValidatePort = False
        Exit Function
    End If
    
    ' Handle comma-separated ports
    ports = Split(portValue, ",")
    
    For i = 0 To UBound(ports)
        Dim trimmedPort As String
        trimmedPort = Trim(ports(i))
        
        If Not IsNumeric(trimmedPort) Then
            ValidatePort = False
            Exit Function
        End If
        
        port = CInt(trimmedPort)
        If port < 1 Or port > 65535 Then
            ValidatePort = False
            Exit Function
        End If
    Next i
    
    ValidatePort = True
End Function

' Validate service name(s)
Function ValidateService(ByVal serviceValue As String) As Boolean
    Dim services() As String
    Dim validServices() As String
    Dim i As Integer, j As Integer
    Dim found As Boolean
    
    ' Check if empty (not allowed)
    If serviceValue = "" Then
        ValidateService = False
        Exit Function
    End If
    
    ' Get valid services list
    validServices = Split(VALID_SERVICES, ",")
    
    ' Handle comma-separated services
    services = Split(serviceValue, ",")
    
    For i = 0 To UBound(services)
        Dim trimmedService As String
        trimmedService = Trim(LCase(services(i)))
        
        found = False
        For j = 0 To UBound(validServices)
            If trimmedService = Trim(LCase(validServices(j))) Then
                found = True
                Exit For
            End If
        Next j
        
        If Not found Then
            ValidateService = False
            Exit Function
        End If
    Next i
    
    ValidateService = True
End Function

' Validate action value
Function ValidateAction(ByVal actionValue As String) As Boolean
    Dim validActions() As String
    Dim i As Integer
    
    ' Check if empty (not allowed)
    If actionValue = "" Then
        ValidateAction = False
        Exit Function
    End If
    
    validActions = Split(VALID_ACTIONS, ",")
    
    For i = 0 To UBound(validActions)
        If actionValue = Trim(validActions(i)) Then
            ValidateAction = True
            Exit Function
        End If
    Next i
    
    ValidateAction = False
End Function

' Validate file type
Function ValidateFileType(ByVal fileTypeValue As String) As Boolean
    Dim fileTypes() As String
    Dim i As Integer
    Dim pattern As String
    
    ' Empty is allowed for file type
    If fileTypeValue = "" Then
        ValidateFileType = True
        Exit Function
    End If
    
    ' Handle comma-separated file types
    fileTypes = Split(fileTypeValue, ",")
    
    For i = 0 To UBound(fileTypes)
        Dim trimmedType As String
        trimmedType = Trim(fileTypes(i))
        
        ' Check if alphanumeric only
        If Not IsAlphaNumeric(trimmedType) Then
            ValidateFileType = False
            Exit Function
        End If
    Next i
    
    ValidateFileType = True
End Function

' Check if string contains only alphanumeric characters
Function IsAlphaNumeric(ByVal text As String) As Boolean
    Dim i As Integer
    Dim char As String
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        If Not ((char >= "A" And char <= "Z") Or (char >= "a" And char <= "z") Or (char >= "0" And char <= "9")) Then
            IsAlphaNumeric = False
            Exit Function
        End If
    Next i
    
    IsAlphaNumeric = True
End Function

' Check if column is required
Function IsRequiredColumn(ByVal columnName As String) As Boolean
    Dim requiredCols() As String
    Dim i As Integer
    
    requiredCols = Split(REQUIRED_COLUMNS, ",")
    
    For i = 0 To UBound(requiredCols)
        If columnName = Trim(requiredCols(i)) Then
            IsRequiredColumn = True
            Exit Function
        End If
    Next i
    
    IsRequiredColumn = False
End Function

' Highlight cell with error
Sub HighlightError(ByVal Target As Range, ByVal errorMessage As String)
    Target.Interior.Color = RGB(255, 200, 200) ' Light red background
    Target.Font.Color = RGB(150, 0, 0) ' Dark red text
    Target.AddComment errorMessage
End Sub

' Validate all data before saving
Function ValidateAllData() As Boolean
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim lastCol As Long
    Dim i As Long, j As Long
    Dim hasErrors As Boolean
    
    Set ws = ActiveSheet
    hasErrors = False
    
    ' Find last row and column with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
    
    ' Clear all previous error formatting
    ws.Range(ws.Cells(2, 1), ws.Cells(lastRow, lastCol)).Interior.Color = xlNone
    ws.Range(ws.Cells(2, 1), ws.Cells(lastRow, lastCol)).Font.Color = vbBlack
    
    ' Clear all comments
    For i = 2 To lastRow
        For j = 1 To lastCol
            If Not ws.Cells(i, j).Comment Is Nothing Then
                ws.Cells(i, j).Comment.Delete
            End If
        Next j
    Next i
    
    ' Validate each data cell
    For i = 2 To lastRow
        For j = 1 To lastCol
            Dim cellRange As Range
            Set cellRange = ws.Cells(i, j)
            Call ValidateChangedCell(cellRange)
            
            ' Check if cell has error formatting
            If cellRange.Interior.Color = RGB(255, 200, 200) Then
                hasErrors = True
            End If
        Next j
    Next i
    
    ValidateAllData = Not hasErrors
End Function

' Setup data validation dropdowns
Sub SetupDataValidation()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim serviceCol As Long, actionCol As Long
    
    Set ws = ActiveSheet
    lastRow = 1000 ' Set up validation for first 1000 rows
    
    ' Find Service and Action columns
    serviceCol = FindColumn("Service")
    actionCol = FindColumn("Action")
    
    ' Setup Service dropdown
    If serviceCol > 0 Then
        With ws.Range(ws.Cells(2, serviceCol), ws.Cells(lastRow, serviceCol)).Validation
            .Delete
            .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
                 Formula1:=VALID_SERVICES
            .IgnoreBlank = False
            .InCellDropdown = True
            .ShowInput = True
            .InputTitle = "Service"
            .InputMessage = "Select a valid service or enter comma-separated services"
            .ShowError = True
            .ErrorTitle = "Invalid Service"
            .ErrorMessage = "Please select from the dropdown or enter valid services: " & VALID_SERVICES
        End With
    End If
    
    ' Setup Action dropdown
    If actionCol > 0 Then
        With ws.Range(ws.Cells(2, actionCol), ws.Cells(lastRow, actionCol)).Validation
            .Delete
            .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
                 Formula1:=VALID_ACTIONS
            .IgnoreBlank = False
            .InCellDropdown = True
            .ShowInput = True
            .InputTitle = "Action"
            .InputMessage = "Select Allow, Deny, or Block"
            .ShowError = True
            .ErrorTitle = "Invalid Action"
            .ErrorMessage = "Please select Allow, Deny, or Block"
        End With
    End If
    
    MsgBox "Data validation dropdowns have been set up for Service and Action columns.", vbInformation, "Setup Complete"
End Sub

' Find column number by header name
Function FindColumn(ByVal columnName As String) As Long
    Dim ws As Worksheet
    Dim lastCol As Long
    Dim i As Long
    
    Set ws = ActiveSheet
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
    
    For i = 1 To lastCol
        If ws.Cells(1, i).Value = columnName Then
            FindColumn = i
            Exit Function
        End If
    Next i
    
    FindColumn = 0
End Function

' Manual validation trigger (can be called from button)
Sub ValidateDataManually()
    Dim result As Boolean
    result = ValidateAllData()
    
    If result Then
        MsgBox "All data validation passed! ✅", vbInformation, "Validation Success"
    Else
        MsgBox "Data validation errors found. Check highlighted cells for details. ❌", vbCritical, "Validation Failed"
    End If
End Sub
