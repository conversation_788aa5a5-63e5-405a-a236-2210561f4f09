#!/usr/bin/env python3
"""
Excel Validation Controls for Network Flow Data

This module provides validation controls for Excel files before they can be saved as CSV.
It ensures that specific data fields are completed correctly according to business rules.
"""

import pandas as pd
from typing import Dict, List, Tuple, Any
import re
import ipaddress


class ExcelValidator:
    def __init__(self, validation_rules_file: str = None):
        """
        Initialize the Excel validator with validation rules.
        
        Args:
            validation_rules_file: Optional JSON file with custom validation rules
        """
        self.validation_rules = self._load_default_rules()
        if validation_rules_file:
            # TODO: Load custom rules from file
            pass
    
    def _load_default_rules(self) -> Dict[str, Any]:
        """Load default validation rules for network flow data."""
        return {
            "required_columns": [
                "Source IP",
                "Destination IP", 
                "Port",
                "Service",
                "Action"
            ],
            "optional_columns": [
                "File Type",
                "Protocol",
                "Description"
            ],
            "validation_rules": {
                "Source IP": {
                    "required": True,
                    "type": "ip_address",
                    "allow_empty": False
                },
                "Destination IP": {
                    "required": True,
                    "type": "ip_address", 
                    "allow_empty": False
                },
                "Port": {
                    "required": True,
                    "type": "port_number",
                    "allow_empty": False,
                    "allow_comma_separated": True
                },
                "Service": {
                    "required": True,
                    "type": "service_name",
                    "allow_empty": False,
                    "allow_comma_separated": True,
                    "valid_values": ["http", "https", "ssh", "ftp", "telnet", "smtp", "dns", "dhcp"]
                },
                "Action": {
                    "required": True,
                    "type": "action",
                    "allow_empty": False,
                    "valid_values": ["Allow", "Deny", "Block"]
                },
                "File Type": {
                    "required": False,
                    "type": "file_extension",
                    "allow_empty": True,
                    "allow_comma_separated": True
                }
            }
        }
    
    def validate_excel_file(self, excel_file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """
        Validate an Excel file against the defined rules.
        
        Args:
            excel_file_path: Path to the Excel file
            sheet_name: Name of the sheet to validate (if None, uses first sheet)
            
        Returns:
            Dictionary containing validation results
        """
        print(f"🔍 Validating Excel file: {excel_file_path}")
        
        try:
            # Read Excel file
            if sheet_name:
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(excel_file_path)
            
            print(f"📊 Loaded {len(df)} rows from Excel file")
            
            # Perform validation
            validation_results = {
                "file_path": excel_file_path,
                "sheet_name": sheet_name,
                "total_rows": len(df),
                "validation_passed": True,
                "errors": [],
                "warnings": [],
                "column_validation": {},
                "row_validation": []
            }
            
            # Validate column structure
            self._validate_columns(df, validation_results)
            
            # Validate data in each row
            self._validate_data_rows(df, validation_results)
            
            # Generate summary
            self._generate_validation_summary(validation_results)
            
            return validation_results
            
        except Exception as e:
            return {
                "file_path": excel_file_path,
                "validation_passed": False,
                "errors": [f"Failed to read Excel file: {str(e)}"],
                "warnings": [],
                "total_rows": 0
            }
    
    def _validate_columns(self, df: pd.DataFrame, results: Dict[str, Any]):
        """Validate that required columns are present."""
        print(f"🔍 Validating column structure...")
        
        available_columns = list(df.columns)
        required_columns = self.validation_rules["required_columns"]
        
        # Check for missing required columns
        missing_columns = []
        for col in required_columns:
            if col not in available_columns:
                missing_columns.append(col)
        
        if missing_columns:
            error_msg = f"Missing required columns: {', '.join(missing_columns)}"
            results["errors"].append(error_msg)
            results["validation_passed"] = False
            print(f"❌ {error_msg}")
        
        # Check for extra columns (warnings)
        all_valid_columns = required_columns + self.validation_rules["optional_columns"]
        extra_columns = [col for col in available_columns if col not in all_valid_columns]
        
        if extra_columns:
            warning_msg = f"Unexpected columns found: {', '.join(extra_columns)}"
            results["warnings"].append(warning_msg)
            print(f"⚠️  {warning_msg}")
        
        results["column_validation"] = {
            "available_columns": available_columns,
            "missing_required": missing_columns,
            "extra_columns": extra_columns
        }
        
        print(f"✅ Column validation completed")
    
    def _validate_data_rows(self, df: pd.DataFrame, results: Dict[str, Any]):
        """Validate data in each row according to field rules."""
        print(f"🔍 Validating data rows...")
        
        validation_rules = self.validation_rules["validation_rules"]
        row_errors = []
        
        for index, row in df.iterrows():
            row_number = index + 2  # Excel row number (accounting for header)
            row_validation = {
                "row_number": row_number,
                "errors": [],
                "warnings": []
            }
            
            # Validate each field in the row
            for column, rules in validation_rules.items():
                if column in df.columns:
                    cell_value = row[column]
                    field_errors = self._validate_field(column, cell_value, rules, row_number)
                    row_validation["errors"].extend(field_errors)
            
            # Add row validation if there are issues
            if row_validation["errors"] or row_validation["warnings"]:
                row_errors.append(row_validation)
                
                # Add to overall errors
                for error in row_validation["errors"]:
                    results["errors"].append(f"Row {row_number}: {error}")
                    results["validation_passed"] = False
        
        results["row_validation"] = row_errors
        print(f"✅ Data validation completed - {len(row_errors)} rows with issues")
    
    def _validate_field(self, field_name: str, value: Any, rules: Dict[str, Any], row_number: int) -> List[str]:
        """Validate a single field value against its rules."""
        errors = []
        
        # Convert to string for processing
        str_value = str(value).strip() if pd.notna(value) else ""
        
        # Check if required field is empty
        if rules.get("required", False) and not rules.get("allow_empty", True):
            if not str_value or str_value.lower() in ["nan", "none", ""]:
                errors.append(f"{field_name} is required but empty")
                return errors  # No point checking further if required field is empty
        
        # Skip validation if empty and empty is allowed
        if not str_value and rules.get("allow_empty", True):
            return errors
        
        # Type-specific validation
        field_type = rules.get("type", "string")
        
        if field_type == "ip_address":
            errors.extend(self._validate_ip_address(field_name, str_value))
        elif field_type == "port_number":
            errors.extend(self._validate_port_number(field_name, str_value, rules.get("allow_comma_separated", False)))
        elif field_type == "service_name":
            errors.extend(self._validate_service_name(field_name, str_value, rules))
        elif field_type == "action":
            errors.extend(self._validate_action(field_name, str_value, rules))
        elif field_type == "file_extension":
            errors.extend(self._validate_file_extension(field_name, str_value, rules.get("allow_comma_separated", False)))
        
        return errors
    
    def _validate_ip_address(self, field_name: str, value: str) -> List[str]:
        """Validate IP address format."""
        errors = []
        try:
            ipaddress.ip_address(value)
        except ValueError:
            errors.append(f"{field_name} '{value}' is not a valid IP address")
        return errors
    
    def _validate_port_number(self, field_name: str, value: str, allow_comma_separated: bool) -> List[str]:
        """Validate port number(s)."""
        errors = []
        
        if allow_comma_separated and ',' in value:
            # Validate each port in comma-separated list
            ports = [p.strip() for p in value.split(',')]
            for port in ports:
                if port:  # Skip empty values
                    errors.extend(self._validate_single_port(field_name, port))
        else:
            errors.extend(self._validate_single_port(field_name, value))
        
        return errors
    
    def _validate_single_port(self, field_name: str, port: str) -> List[str]:
        """Validate a single port number."""
        errors = []
        try:
            port_num = int(port)
            if not (1 <= port_num <= 65535):
                errors.append(f"{field_name} '{port}' must be between 1 and 65535")
        except ValueError:
            errors.append(f"{field_name} '{port}' is not a valid port number")
        return errors
    
    def _validate_service_name(self, field_name: str, value: str, rules: Dict[str, Any]) -> List[str]:
        """Validate service name(s)."""
        errors = []
        valid_values = rules.get("valid_values", [])
        allow_comma_separated = rules.get("allow_comma_separated", False)
        
        if allow_comma_separated and ',' in value:
            # Validate each service in comma-separated list
            services = [s.strip().lower() for s in value.split(',')]
            for service in services:
                if service and valid_values and service not in valid_values:
                    errors.append(f"{field_name} '{service}' is not in allowed values: {', '.join(valid_values)}")
        else:
            if valid_values and value.lower() not in valid_values:
                errors.append(f"{field_name} '{value}' is not in allowed values: {', '.join(valid_values)}")
        
        return errors
    
    def _validate_action(self, field_name: str, value: str, rules: Dict[str, Any]) -> List[str]:
        """Validate action value."""
        errors = []
        valid_values = rules.get("valid_values", [])
        
        if valid_values and value not in valid_values:
            errors.append(f"{field_name} '{value}' is not in allowed values: {', '.join(valid_values)}")
        
        return errors
    
    def _validate_file_extension(self, field_name: str, value: str, allow_comma_separated: bool) -> List[str]:
        """Validate file extension(s)."""
        errors = []
        
        if allow_comma_separated and ',' in value:
            # Validate each extension in comma-separated list
            extensions = [ext.strip() for ext in value.split(',')]
            for ext in extensions:
                if ext:  # Skip empty values
                    errors.extend(self._validate_single_extension(field_name, ext))
        else:
            errors.extend(self._validate_single_extension(field_name, value))
        
        return errors
    
    def _validate_single_extension(self, field_name: str, extension: str) -> List[str]:
        """Validate a single file extension."""
        errors = []
        
        # Basic format check - should be alphanumeric
        if not re.match(r'^[a-zA-Z0-9]+$', extension):
            errors.append(f"{field_name} '{extension}' contains invalid characters (use only letters and numbers)")
        
        return errors
    
    def _generate_validation_summary(self, results: Dict[str, Any]):
        """Generate and display validation summary."""
        print(f"\n📊 Validation Summary:")
        print(f"   Total rows: {results['total_rows']}")
        print(f"   Validation passed: {'✅' if results['validation_passed'] else '❌'}")
        print(f"   Errors: {len(results['errors'])}")
        print(f"   Warnings: {len(results['warnings'])}")
        
        if results['errors']:
            print(f"\n❌ Errors found:")
            for error in results['errors'][:10]:  # Show first 10 errors
                print(f"   • {error}")
            if len(results['errors']) > 10:
                print(f"   ... and {len(results['errors']) - 10} more errors")
        
        if results['warnings']:
            print(f"\n⚠️  Warnings:")
            for warning in results['warnings']:
                print(f"   • {warning}")
    
    def can_save_as_csv(self, validation_results: Dict[str, Any]) -> bool:
        """
        Determine if the Excel file can be saved as CSV based on validation results.
        
        Args:
            validation_results: Results from validate_excel_file()
            
        Returns:
            True if file can be saved as CSV, False otherwise
        """
        return validation_results.get("validation_passed", False)
    
    def save_as_csv_if_valid(self, excel_file_path: str, csv_output_path: str, sheet_name: str = None) -> bool:
        """
        Validate Excel file and save as CSV only if validation passes.
        
        Args:
            excel_file_path: Path to Excel file
            csv_output_path: Path for CSV output
            sheet_name: Sheet name to convert
            
        Returns:
            True if successfully saved, False if validation failed
        """
        print(f"🔄 Validating and converting Excel to CSV...")
        
        # Validate first
        validation_results = self.validate_excel_file(excel_file_path, sheet_name)
        
        if self.can_save_as_csv(validation_results):
            try:
                # Read and save as CSV
                if sheet_name:
                    df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                else:
                    df = pd.read_excel(excel_file_path)
                
                df.to_csv(csv_output_path, index=False)
                print(f"✅ Successfully saved as CSV: {csv_output_path}")
                return True
                
            except Exception as e:
                print(f"❌ Failed to save CSV: {str(e)}")
                return False
        else:
            print(f"❌ Cannot save as CSV - validation failed")
            print(f"   Please fix the {len(validation_results['errors'])} validation errors first")
            return False


# Example usage and testing
if __name__ == "__main__":
    validator = ExcelValidator()
    
    # Example validation (would need actual Excel file)
    print("Excel Validator initialized with default rules")
    print("Use validator.validate_excel_file('your_file.xlsx') to validate")
    print("Use validator.save_as_csv_if_valid('input.xlsx', 'output.csv') to convert")
