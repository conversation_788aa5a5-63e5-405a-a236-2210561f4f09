#!/usr/bin/env python3
"""
Tufin Rule Recertification - Deployment File Lister

This script helps identify which files are needed for production deployment
vs. which files are only for testing/development.
"""

import os
import sys
from pathlib import Path

def get_file_size(filepath):
    """Get file size in a human-readable format"""
    try:
        size = os.path.getsize(filepath)
        for unit in ['B', 'KB', 'MB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} GB"
    except OSError:
        return "N/A"

def main():
    """List files categorized by deployment necessity"""
    
    # Define file categories
    core_files = [
        'tufin_rule_recertification.py',
        'tufin_api_client.py', 
        'rule_owner_manager.py',
        'securechange_ticket_manager.py',
        'requirements.txt'
    ]
    
    config_files = [
        'tufin_config.json'
    ]
    
    documentation_files = [
        'TUFIN_README.md',
        'DEPLOYMENT_GUIDE.md'
    ]
    
    example_files = [
        'tufin_example_usage.py'
    ]
    
    test_files = [
        'dummy_tufin_data.json',
        'mock_tufin_api_client.py',
        'test_config.json',
        'run_local_test.py',
        'test_rule_ownership.py'
    ]
    
    # Get current directory
    current_dir = Path('.')
    
    print("=" * 80)
    print("TUFIN RULE RECERTIFICATION - DEPLOYMENT FILE ANALYSIS")
    print("=" * 80)
    
    def print_file_category(title, files, required=True):
        """Print a category of files with their status"""
        print(f"\n📁 {title}")
        print("-" * 60)
        
        found_files = []
        missing_files = []
        
        for filename in files:
            filepath = current_dir / filename
            if filepath.exists():
                size = get_file_size(filepath)
                status = "✅ FOUND" if required else "📄 FOUND"
                print(f"   {status:<12} {filename:<35} ({size})")
                found_files.append(filename)
            else:
                status = "❌ MISSING" if required else "⚪ MISSING"
                print(f"   {status:<12} {filename}")
                missing_files.append(filename)
        
        if required and missing_files:
            print(f"\n   ⚠️  WARNING: {len(missing_files)} required files missing!")
        
        return found_files, missing_files
    
    # Analyze each category
    core_found, core_missing = print_file_category("CORE SYSTEM FILES (Required)", core_files, True)
    config_found, config_missing = print_file_category("CONFIGURATION FILES (Required)", config_files, True)
    doc_found, doc_missing = print_file_category("DOCUMENTATION (Recommended)", documentation_files, False)
    example_found, example_missing = print_file_category("EXAMPLES (Optional)", example_files, False)
    test_found, test_missing = print_file_category("TEST/DEVELOPMENT FILES (Not needed for production)", test_files, False)
    
    # Summary
    print("\n" + "=" * 80)
    print("DEPLOYMENT READINESS SUMMARY")
    print("=" * 80)
    
    total_required = len(core_files) + len(config_files)
    total_found = len(core_found) + len(config_found)
    
    print(f"📊 Required files: {total_found}/{total_required} found")
    
    if total_found == total_required:
        print("✅ READY FOR DEPLOYMENT")
        print("\n📦 Files to copy for production deployment:")
        for filename in core_found + config_found:
            print(f"   • {filename}")
    else:
        print("❌ NOT READY - Missing required files")
        all_missing = core_missing + config_missing
        print(f"\n📋 Missing required files:")
        for filename in all_missing:
            print(f"   • {filename}")
    
    print(f"\n📄 Optional documentation files available: {len(doc_found)}")
    print(f"🧪 Test/development files present: {len(test_found)} (can be excluded from production)")
    
    # Deployment command suggestion
    if total_found == total_required:
        print("\n" + "=" * 80)
        print("SUGGESTED DEPLOYMENT COMMANDS")
        print("=" * 80)
        print("\n# Create deployment directory and copy core files:")
        print("mkdir tufin_recertification")
        print("cd tufin_recertification")
        print()
        for filename in core_found + config_found:
            print(f"cp ../{filename} .")
        print()
        print("# Install dependencies:")
        print("pip install -r requirements.txt")
        print()
        print("# Configure and test:")
        print("cp tufin_config.json production_config.json")
        print("# Edit production_config.json with your settings")
        print("python tufin_rule_recertification.py --config production_config.json --dry-run")

if __name__ == "__main__":
    main()
