2025-07-12 10:31:30,418 - __main__ - INFO - Configuration loaded successfully
2025-07-12 10:31:30,428 - mock_tufin_api_client - INFO - Loaded dummy data from dummy_tufin_data.json
2025-07-12 10:31:30,428 - mock_tufin_api_client - INFO - Mock Tufin API Client initialized with dummy data
2025-07-12 10:31:30,429 - mock_tufin_api_client - INFO - Returning 5 mock devices
2025-07-12 10:31:30,429 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1001
2025-07-12 10:31:30,451 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1002
2025-07-12 10:31:30,451 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1003
2025-07-12 10:31:30,452 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1004
2025-07-12 10:31:30,452 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1005
2025-07-12 10:31:30,453 - mock_tufin_api_client - INFO - Found 27 mock expiring rules
2025-07-12 10:32:32,540 - __main__ - INFO - Configuration loaded successfully
2025-07-12 10:32:32,541 - mock_tufin_api_client - INFO - Loaded dummy data from dummy_tufin_data.json
2025-07-12 10:32:32,541 - mock_tufin_api_client - INFO - Mock Tufin API Client initialized with dummy data
2025-07-12 10:32:32,542 - mock_tufin_api_client - INFO - Returning 5 mock devices
2025-07-12 10:32:32,542 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1001
2025-07-12 10:32:32,548 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1002
2025-07-12 10:32:32,549 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1003
2025-07-12 10:32:32,549 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1004
2025-07-12 10:32:32,550 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1005
2025-07-12 10:32:32,550 - mock_tufin_api_client - INFO - Found 27 mock expiring rules
2025-07-12 10:32:32,551 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,551 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,551 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,551 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,551 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,552 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,552 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,552 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,552 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,552 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,552 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,553 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,553 - rule_owner_manager - WARNING - Could not <NAME_EMAIL>: 'MockTufinAPIClient' object has no attribute 'securechange_api'
2025-07-12 10:32:32,553 - rule_owner_manager - INFO - Owner validation summary: {'rules_with_owners': 0, 'rules_without_owners': 27, 'unique_owners': 0, 'owner_distribution': {}}
2025-07-12 10:34:14,817 - __main__ - INFO - Configuration loaded successfully
2025-07-12 10:34:14,818 - mock_tufin_api_client - INFO - Loaded dummy data from dummy_tufin_data.json
2025-07-12 10:34:14,818 - mock_tufin_api_client - INFO - Mock Tufin API Client initialized with dummy data
2025-07-12 10:34:14,819 - mock_tufin_api_client - INFO - Returning 5 mock devices
2025-07-12 10:34:14,819 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1001
2025-07-12 10:34:14,825 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1002
2025-07-12 10:34:14,825 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1003
2025-07-12 10:34:14,826 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1004
2025-07-12 10:34:14,826 - mock_tufin_api_client - INFO - Returning 3 mock rules for device 1005
2025-07-12 10:34:14,826 - mock_tufin_api_client - INFO - Found 27 mock expiring rules
2025-07-12 10:34:14,827 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,827 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,827 - mock_tufin_api_client - WARNING - Mock user not found: <EMAIL>
2025-07-12 10:34:14,827 - rule_owner_manager - WARNING - User <EMAIL> exists but cannot receive assignments
2025-07-12 10:34:14,827 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,828 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1002-001
2025-07-12 10:34:14,828 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1002-001
2025-07-12 10:34:14,828 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,828 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,828 - mock_tufin_api_client - WARNING - Mock user not found: <EMAIL>
2025-07-12 10:34:14,828 - rule_owner_manager - WARNING - User <EMAIL> exists but cannot receive assignments
2025-07-12 10:34:14,828 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1003-001
2025-07-12 10:34:14,829 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1003-001
2025-07-12 10:34:14,829 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,829 - mock_tufin_api_client - WARNING - Mock user not found: <EMAIL>
2025-07-12 10:34:14,829 - rule_owner_manager - WARNING - User <EMAIL> exists but cannot receive assignments
2025-07-12 10:34:14,829 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1003-003
2025-07-12 10:34:14,829 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1003-003
2025-07-12 10:34:14,830 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,830 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,830 - mock_tufin_api_client - INFO - Found mock user: <EMAIL>
2025-07-12 10:34:14,830 - mock_tufin_api_client - WARNING - Mock user not found: <EMAIL>
2025-07-12 10:34:14,830 - rule_owner_manager - WARNING - User <EMAIL> exists but cannot receive assignments
2025-07-12 10:34:14,831 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1005-002
2025-07-12 10:34:14,831 - rule_owner_manager - INFO - Using <NAME_EMAIL> for rule R1005-002
2025-07-12 10:34:14,831 - rule_owner_manager - INFO - Owner validation summary: {'rules_with_owners': 27, 'rules_without_owners': 0, 'unique_owners': 9, 'owner_distribution': {'<EMAIL>': 2, '<EMAIL>': 3, '<EMAIL>': 8, '<EMAIL>': 2, '<EMAIL>': 2, '<EMAIL>': 2, '<EMAIL>': 3, '<EMAIL>': 2, '<EMAIL>': 3}}
2025-07-12 10:34:14,832 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1001 for rule R1001-001
2025-07-12 10:34:14,833 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1002 for rule R1001-001
2025-07-12 10:34:14,833 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1003 for rule R1001-002
2025-07-12 10:34:14,833 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1004 for rule R1001-002
2025-07-12 10:34:14,833 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1005 for rule R1001-003
2025-07-12 10:34:14,833 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1006 for rule R1002-001
2025-07-12 10:34:14,833 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1007 for rule R1002-001
2025-07-12 10:34:14,834 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1008 for rule R1002-002
2025-07-12 10:34:14,834 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1009 for rule R1002-002
2025-07-12 10:34:14,834 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1010 for rule R1002-003
2025-07-12 10:34:14,834 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1011 for rule R1002-003
2025-07-12 10:34:14,834 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1012 for rule R1003-001
2025-07-12 10:34:14,834 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1013 for rule R1003-001
2025-07-12 10:34:14,835 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1014 for rule R1003-002
2025-07-12 10:34:14,835 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1015 for rule R1003-002
2025-07-12 10:34:14,835 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1016 for rule R1003-003
2025-07-12 10:34:14,835 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1017 for rule R1003-003
2025-07-12 10:34:14,835 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1018 for rule R1004-001
2025-07-12 10:34:14,836 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1019 for rule R1004-001
2025-07-12 10:34:14,836 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1020 for rule R1004-002
2025-07-12 10:34:14,836 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1021 for rule R1004-002
2025-07-12 10:34:14,836 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1022 for rule R1004-003
2025-07-12 10:34:14,836 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1023 for rule R1005-001
2025-07-12 10:34:14,836 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1024 for rule R1005-001
2025-07-12 10:34:14,837 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1025 for rule R1005-002
2025-07-12 10:34:14,837 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1026 for rule R1005-002
2025-07-12 10:34:14,837 - __main__ - INFO - Mock ticket created: MOCK-RECERT-1027 for rule R1005-003
