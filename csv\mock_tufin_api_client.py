"""
Mock Tufin API Client for Testing

This module provides a mock implementation of the Tufin API client
that uses dummy data instead of making real API calls. Perfect for
testing the rule ownership detection and ticket creation logic.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

class MockTufinAPIClient:
    """
    Mock Tufin API Client that uses dummy data for testing
    
    This client simulates the behavior of the real TufinAPIClient
    but uses local JSON data instead of making HTTP requests.
    """
    
    def __init__(self, base_url: str = "https://mock-tufin.local", 
                 username: str = "test-user", password: str = "test-pass",
                 verify_ssl: bool = False, timeout: int = 30,
                 dummy_data_file: str = "dummy_tufin_data.json"):
        """
        Initialize the mock API client
        
        Args:
            base_url: Mock base URL (not used)
            username: Mock username (not used)
            password: Mock password (not used)
            verify_ssl: Mock SSL setting (not used)
            timeout: Mock timeout (not used)
            dummy_data_file: Path to dummy data JSON file
        """
        self.base_url = base_url
        self.username = username
        self.password = password
        self.verify_ssl = verify_ssl
        self.timeout = timeout
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
        # Load dummy data
        self.dummy_data_file = dummy_data_file
        self._load_dummy_data()
        
        # Mock authentication state
        self._auth_token = "mock-token-12345"
        self._token_expires = datetime.now() + timedelta(hours=24)
        
        self.logger.info("Mock Tufin API Client initialized with dummy data")
    
    def _load_dummy_data(self):
        """Load dummy data from JSON file"""
        try:
            dummy_data_path = Path(self.dummy_data_file)
            if not dummy_data_path.exists():
                # Try relative to current script
                dummy_data_path = Path(__file__).parent / self.dummy_data_file
            
            with open(dummy_data_path, 'r', encoding='utf-8') as f:
                self.dummy_data = json.load(f)
            
            self.logger.info(f"Loaded dummy data from {dummy_data_path}")
            
        except FileNotFoundError:
            self.logger.error(f"Dummy data file not found: {self.dummy_data_file}")
            self.dummy_data = {"devices": [], "rules": {}, "users": []}
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing dummy data JSON: {e}")
            self.dummy_data = {"devices": [], "rules": {}, "users": []}
    
    def authenticate(self) -> bool:
        """
        Mock authentication - always succeeds
        
        Returns:
            bool: Always True for mock client
        """
        self.logger.info("Mock authentication successful")
        return True
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Mock connection test
        
        Returns:
            Dict: Mock connection test results
        """
        return {
            'success': True,
            'message': 'Successfully connected to Mock Tufin API',
            'system_info': {
                'version': 'Mock Tufin R81.20',
                'build': 'mock-build-001',
                'hostname': 'mock-tufin-server'
            }
        }
    
    def get_devices(self, domain_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get list of mock devices, optionally filtered by domain

        Args:
            domain_id: Optional domain ID to filter devices

        Returns:
            List[Dict]: List of device information from dummy data
        """
        devices = self.dummy_data.get('devices', [])

        if domain_id is not None:
            # Filter devices by domain_id
            filtered_devices = [d for d in devices if d.get('domain_id') == domain_id]
            self.logger.info(f"Returning {len(filtered_devices)} mock devices for domain {domain_id}")
            return filtered_devices

        self.logger.info(f"Returning {len(devices)} mock devices (all domains)")
        return devices

    def get_domains(self) -> List[Dict[str, Any]]:
        """
        Get list of mock domains

        Returns:
            List[Dict]: List of domain information
        """
        domains = self.dummy_data.get('domains', [
            {'id': 1, 'name': 'Domain 1', 'description': 'Primary security domain'},
            {'id': 2, 'name': 'Domain 2', 'description': 'Secondary security domain'},
            {'id': 3, 'name': 'DMZ Domain', 'description': 'DMZ security domain'}
        ])
        self.logger.info(f"Returning {len(domains)} mock domains")
        return domains

    def get_device_rules(self, device_id: int, include_disabled: bool = False) -> List[Dict[str, Any]]:
        """
        Get mock rules for a specific device
        
        Args:
            device_id: ID of the device
            include_disabled: Whether to include disabled rules (ignored in mock)
            
        Returns:
            List[Dict]: List of rules for the device from dummy data
        """
        rules = self.dummy_data.get('rules', {}).get(str(device_id), [])
        
        # Filter out disabled rules if requested
        if not include_disabled:
            rules = [rule for rule in rules if rule.get('enabled', True)]
        
        self.logger.info(f"Returning {len(rules)} mock rules for device {device_id}")
        return rules
    
    def get_rules_with_expiration(self, days_ahead: int = 30, domain_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get mock rules that have expiration dates within the specified number of days

        Args:
            days_ahead: Number of days to look ahead for expiring rules
            domain_id: Optional domain ID to filter devices

        Returns:
            List[Dict]: List of rules with expiration information
        """
        expiring_rules = []
        target_date = datetime.now() + timedelta(days=days_ahead)

        # Get devices (filtered by domain if specified)
        devices = self.get_devices(domain_id=domain_id)
        
        for device in devices:
            device_id = device.get('id')
            device_name = device.get('name', 'Unknown')
            
            # Get rules for this device
            rules = self.get_device_rules(device_id, include_disabled=False)
            
            for rule in rules:
                rule_expiration = self._parse_rule_expiration(rule)
                certification_expiration = self._parse_certification_expiration(rule)
                
                # Check if rule expires within the target timeframe
                if rule_expiration and rule_expiration <= target_date:
                    expiring_rules.append({
                        'device_id': device_id,
                        'device_name': device_name,
                        'rule_id': rule.get('id'),
                        'rule_name': rule.get('name', ''),
                        'rule_number': rule.get('number'),
                        'expiration_date': rule_expiration,
                        'expiration_type': 'rule',
                        'days_until_expiration': (rule_expiration - datetime.now()).days,
                        'rule_owner': rule.get('owner', ''),
                        'rule_data': rule
                    })
                
                # Check if certification expires within the target timeframe
                if certification_expiration and certification_expiration <= target_date:
                    expiring_rules.append({
                        'device_id': device_id,
                        'device_name': device_name,
                        'rule_id': rule.get('id'),
                        'rule_name': rule.get('name', ''),
                        'rule_number': rule.get('number'),
                        'expiration_date': certification_expiration,
                        'expiration_type': 'certification',
                        'days_until_expiration': (certification_expiration - datetime.now()).days,
                        'rule_owner': rule.get('owner', ''),
                        'rule_data': rule
                    })
        
        self.logger.info(f"Found {len(expiring_rules)} mock expiring rules")
        return expiring_rules
    
    def _parse_rule_expiration(self, rule: Dict[str, Any]) -> Optional[datetime]:
        """
        Parse rule expiration date from mock rule data
        
        Args:
            rule: Rule data from dummy data
            
        Returns:
            datetime: Expiration date if found, None otherwise
        """
        expiration_date = rule.get('expiration_date')
        if expiration_date:
            try:
                return datetime.strptime(expiration_date, '%Y-%m-%d')
            except ValueError:
                return None
        return None
    
    def _parse_certification_expiration(self, rule: Dict[str, Any]) -> Optional[datetime]:
        """
        Parse certification expiration date from mock rule data
        
        Args:
            rule: Rule data from dummy data
            
        Returns:
            datetime: Certification expiration date if found, None otherwise
        """
        cert_expiry = rule.get('certification_expiry')
        if cert_expiry:
            try:
                return datetime.strptime(cert_expiry, '%Y-%m-%d')
            except ValueError:
                return None
        
        # Check last_certified and add 1 year
        last_certified = rule.get('last_certified')
        if last_certified:
            try:
                last_cert_date = datetime.strptime(last_certified, '%Y-%m-%d')
                return last_cert_date + timedelta(days=365)
            except ValueError:
                return None
        
        return None
    
    def get_user_info(self, username: str) -> Dict[str, Any]:
        """
        Get mock user information

        Args:
            username: Username to look up

        Returns:
            Dict: User information from dummy data
        """
        users = self.dummy_data.get('users', [])

        for user in users:
            if user.get('username', '').lower() == username.lower():
                self.logger.info(f"Found mock user: {username}")
                return user

        # Return mock "not found" user
        self.logger.warning(f"Mock user not found: {username}")
        return {
            'username': username,
            'active': False,
            'can_receive_assignments': False,
            'full_name': 'Unknown User',
            'department': 'Unknown'
        }

    @property
    def securechange_api(self):
        """Mock SecureChange API property for compatibility"""
        return self
    
    def _make_request(self, method: str, url: str, **kwargs) -> 'MockResponse':
        """
        Mock HTTP request - returns mock response objects
        
        Args:
            method: HTTP method
            url: URL (analyzed to determine response)
            **kwargs: Additional arguments (ignored)
            
        Returns:
            MockResponse: Mock response object
        """
        self.logger.debug(f"Mock {method} request to {url}")
        
        # Simulate different endpoints
        if '/users/' in url:
            username = url.split('/users/')[-1]
            user_data = self.get_user_info(username)
            return MockResponse(200, user_data)
        elif '/devices' in url:
            return MockResponse(200, {'devices': self.get_devices()})
        elif '/system/info' in url:
            return MockResponse(200, self.test_connection()['system_info'])
        else:
            return MockResponse(200, {'message': 'Mock response'})


class MockResponse:
    """Mock HTTP response object"""
    
    def __init__(self, status_code: int, json_data: Dict[str, Any]):
        self.status_code = status_code
        self._json_data = json_data
    
    def json(self) -> Dict[str, Any]:
        """Return JSON data"""
        return self._json_data
