#!/usr/bin/env python3
"""
Quick check for exe detection issues.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

def quick_exe_check(csv_file: str):
    """Quick check for exe detection."""
    
    print("🔍 QUICK EXE CHECK")
    print("=" * 30)
    
    # 1. Check configuration files exist
    try:
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        print("✅ Configuration files loaded")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return
    
    # 2. Check if exe is in blocked list
    file_type_rules = validator.unacceptable_values.get("File Type", {})
    blocked = file_type_rules.get("blocked", [])
    
    if "exe" in [f.lower() for f in blocked]:
        print("✅ 'exe' is in blocked list")
    else:
        print(f"❌ 'exe' NOT in blocked list: {blocked}")
        return
    
    # 3. Test different skip_rows values
    print(f"\n📁 Testing CSV file: {csv_file}")
    
    for skip_rows in [0, 31, 32]:
        try:
            flow_data = csv_to_dict_simple(csv_file, skip_rows=skip_rows)
            if flow_data:
                sample = next(iter(flow_data.values()))
                has_file_type = "File Type" in sample
                
                print(f"  skip_rows={skip_rows}: {len(flow_data)} flows, File Type column: {has_file_type}")
                
                if has_file_type:
                    # Check for exe values
                    exe_count = 0
                    for flow_data_item in flow_data.values():
                        file_type = flow_data_item.get("File Type", "").lower()
                        if "exe" in file_type:
                            exe_count += 1
                    
                    print(f"    Found {exe_count} flows with 'exe' in File Type")
                    
                    if exe_count > 0:
                        print(f"    ✅ This looks correct - use skip_rows={skip_rows}")
                        
                        # Quick validation test
                        results = validator.validate_flow_data(flow_data)
                        critical_issues = results.get("critical_issues", 0)
                        print(f"    Validation result: {critical_issues} critical issues")
                        
                        if critical_issues == 0:
                            print(f"    ❌ NO CRITICAL ISSUES FOUND - This is the problem!")
                        else:
                            print(f"    ✅ Critical issues found - validation working")
                        
                        return skip_rows
            else:
                print(f"  skip_rows={skip_rows}: No data loaded")
        except Exception as e:
            print(f"  skip_rows={skip_rows}: Error - {e}")
    
    print(f"\n❌ Could not find valid configuration")
    return None

if __name__ == "__main__":
    csv_file = input("Enter your CSV file path: ").strip()
    if csv_file:
        quick_exe_check(csv_file)
    else:
        print("No file specified")
