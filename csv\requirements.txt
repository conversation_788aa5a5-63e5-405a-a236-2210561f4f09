# Tufin Rule Recertification Script Dependencies

# Core HTTP library for API calls
requests>=2.28.0

# Date and time utilities (built-in, but listed for clarity)
# datetime - built-in module

# JSON handling (built-in)
# json - built-in module

# Logging (built-in)
# logging - built-in module

# Regular expressions (built-in)
# re - built-in module

# Type hints (built-in in Python 3.5+)
# typing - built-in module

# Path handling (built-in in Python 3.4+)
# pathlib - built-in module

# URL parsing (built-in)
# urllib - built-in module

# Optional: Enhanced logging and formatting
colorlog>=6.7.0

# Optional: Configuration validation
jsonschema>=4.17.0

# Optional: Email notifications
smtplib  # built-in module

# Optional: Enhanced CLI argument parsing
argparse  # built-in module

# Optional: Progress bars for long operations
tqdm>=4.64.0

# Optional: Better error handling and debugging
rich>=13.0.0
