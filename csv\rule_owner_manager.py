"""
Rule Owner Management for Tufin Recertification

This module handles the identification and validation of rule owners
for automatic ticket assignment.
"""

import re
import logging
from typing import Dict, List, Optional, Any, Set
from tufin_api_client import TufinAPIClient, TufinAPIError

class RuleOwnerManager:
    """
    Manager for rule owner identification and validation
    
    This class handles the logic for determining rule owners and
    validating them for ticket assignment.
    """
    
    def __init__(self, api_client: TufinAPIClient, 
                 default_owner: Optional[str] = None,
                 owner_mapping: Optional[Dict[str, str]] = None):
        """
        Initialize the rule owner manager
        
        Args:
            api_client: Authenticated TufinAPIClient instance
            default_owner: Default owner for rules without specific owners
            owner_mapping: Custom mapping of rule patterns to owners
        """
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self.default_owner = default_owner
        self.owner_mapping = owner_mapping or {}
        
        # Cache for validated users
        self._validated_users = set()
        self._invalid_users = set()
    
    def get_rule_owner(self, rule_info: Dict[str, Any]) -> Optional[str]:
        """
        Determine the owner of a rule based on various criteria
        
        Args:
            rule_info: Rule information dictionary
            
        Returns:
            str: Owner username/email if found, None otherwise
        """
        # Priority order for owner determination:
        # 1. Explicit rule owner field
        # 2. Custom owner mapping based on rule patterns
        # 3. Device-based owner mapping
        # 4. Rule name/description pattern matching
        # 5. Default owner
        
        # 1. Check explicit rule owner
        explicit_owner = rule_info.get('rule_owner')
        if explicit_owner and explicit_owner.strip():
            return self._normalize_username(explicit_owner.strip())
        
        # 2. Check custom owner mapping
        mapped_owner = self._check_owner_mapping(rule_info)
        if mapped_owner:
            return mapped_owner
        
        # 3. Check device-based owner
        device_owner = self._get_device_owner(rule_info.get('device_id'))
        if device_owner:
            return device_owner
        
        # 4. Pattern-based owner detection
        pattern_owner = self._detect_owner_from_patterns(rule_info)
        if pattern_owner:
            return pattern_owner
        
        # 5. Return default owner
        return self.default_owner
    
    def _normalize_username(self, username: str) -> str:
        """
        Normalize username format
        
        Args:
            username: Raw username
            
        Returns:
            str: Normalized username
        """
        # Remove common prefixes/suffixes
        username = username.strip()
        
        # Handle domain\username format
        if '\\' in username:
            username = username.split('\\')[-1]
        
        # Handle email format
        if '@' in username:
            # Keep full email for now, but could extract just username part
            pass
        
        return username.lower()
    
    def _check_owner_mapping(self, rule_info: Dict[str, Any]) -> Optional[str]:
        """
        Check custom owner mapping for rule patterns
        
        Args:
            rule_info: Rule information
            
        Returns:
            str: Mapped owner if found, None otherwise
        """
        rule_name = rule_info.get('rule_name', '').lower()
        device_name = rule_info.get('device_name', '').lower()
        
        # Check rule name patterns
        for pattern, owner in self.owner_mapping.items():
            if pattern.lower() in rule_name or pattern.lower() in device_name:
                return self._normalize_username(owner)
        
        return None
    
    def _get_device_owner(self, device_id: int) -> Optional[str]:
        """
        Get the owner of a device
        
        Args:
            device_id: Device ID
            
        Returns:
            str: Device owner if found, None otherwise
        """
        try:
            url = f"{self.api_client.securetrack_api}/devices/{device_id}"
            response = self.api_client._make_request('GET', url)
            device_data = response.json()
            
            # Look for owner fields in device data
            owner_fields = ['owner', 'administrator', 'contact', 'responsible_person']
            for field in owner_fields:
                if field in device_data and device_data[field]:
                    return self._normalize_username(device_data[field])
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Could not get device owner for device {device_id}: {str(e)}")
            return None
    
    def _detect_owner_from_patterns(self, rule_info: Dict[str, Any]) -> Optional[str]:
        """
        Detect owner from rule name/description patterns
        
        Args:
            rule_info: Rule information
            
        Returns:
            str: Detected owner if found, None otherwise
        """
        rule_name = rule_info.get('rule_name', '')
        rule_data = rule_info.get('rule_data', {})
        description = rule_data.get('description', '')
        
        # Common patterns for owner detection
        patterns = [
            r'owner[:\s]+([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+)',  # Email in owner field
            r'contact[:\s]+([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+)',  # Email in contact field
            r'created[:\s]+by[:\s]+([a-zA-Z0-9._-]+)',  # Created by username
            r'([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+)',  # Any email address
        ]
        
        text_to_search = f"{rule_name} {description}".lower()
        
        for pattern in patterns:
            matches = re.findall(pattern, text_to_search, re.IGNORECASE)
            if matches:
                return self._normalize_username(matches[0])
        
        return None
    
    def validate_user(self, username: str) -> bool:
        """
        Validate if a user exists and can be assigned tickets
        
        Args:
            username: Username to validate
            
        Returns:
            bool: True if user is valid, False otherwise
        """
        if not username:
            return False
        
        normalized_user = self._normalize_username(username)
        
        # Check cache first
        if normalized_user in self._validated_users:
            return True
        if normalized_user in self._invalid_users:
            return False
        
        try:
            # Try to get user information from Tufin
            url = f"{self.api_client.securechange_api}/users/{normalized_user}"
            response = self.api_client._make_request('GET', url)
            
            if response.status_code == 200:
                user_data = response.json()
                # Check if user is active and can receive assignments
                if user_data.get('active', True) and user_data.get('can_receive_assignments', True):
                    self._validated_users.add(normalized_user)
                    return True
                else:
                    self.logger.warning(f"User {normalized_user} exists but cannot receive assignments")
                    self._invalid_users.add(normalized_user)
                    return False
            else:
                self._invalid_users.add(normalized_user)
                return False
                
        except Exception as e:
            self.logger.warning(f"Could not validate user {normalized_user}: {str(e)}")
            self._invalid_users.add(normalized_user)
            return False
    
    def get_validated_owner(self, rule_info: Dict[str, Any]) -> Optional[str]:
        """
        Get a validated owner for a rule
        
        Args:
            rule_info: Rule information
            
        Returns:
            str: Validated owner if found, None otherwise
        """
        potential_owner = self.get_rule_owner(rule_info)
        
        if potential_owner and self.validate_user(potential_owner):
            return potential_owner
        
        # If primary owner is invalid, try default owner
        if self.default_owner and potential_owner != self.default_owner:
            if self.validate_user(self.default_owner):
                self.logger.info(f"Using default owner {self.default_owner} for rule {rule_info.get('rule_id')}")
                return self.default_owner
        
        return None
    
    def bulk_validate_owners(self, rules_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate owners for multiple rules
        
        Args:
            rules_list: List of rule information
            
        Returns:
            Dict: Validation results
        """
        results = {
            'total_rules': len(rules_list),
            'rules_with_valid_owners': [],
            'rules_without_owners': [],
            'validation_summary': {}
        }
        
        owner_counts = {}
        
        for rule_info in rules_list:
            validated_owner = self.get_validated_owner(rule_info)
            
            if validated_owner:
                rule_info['validated_owner'] = validated_owner
                results['rules_with_valid_owners'].append(rule_info)
                owner_counts[validated_owner] = owner_counts.get(validated_owner, 0) + 1
            else:
                results['rules_without_owners'].append(rule_info)
        
        results['validation_summary'] = {
            'rules_with_owners': len(results['rules_with_valid_owners']),
            'rules_without_owners': len(results['rules_without_owners']),
            'unique_owners': len(owner_counts),
            'owner_distribution': owner_counts
        }
        
        self.logger.info(f"Owner validation summary: {results['validation_summary']}")
        
        return results
