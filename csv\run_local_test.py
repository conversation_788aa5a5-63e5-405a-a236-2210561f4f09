#!/usr/bin/env python3
"""
Local Test Runner for Tufin Rule Recertification System

This script runs the complete Tufin recertification system using
dummy data for local testing and demonstration purposes.
"""

import json
import logging
import sys
from datetime import datetime
from pathlib import Path

# Import our modules
from mock_tufin_api_client import MockTufinAPIClient
from rule_owner_manager import RuleOwnerManager

def setup_logging():
    """Setup comprehensive logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('local_test.log')
        ]
    )

def load_test_config():
    """Load test configuration"""
    try:
        with open('test_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ test_config.json not found. Please run this script from the csv directory.")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing test_config.json: {e}")
        sys.exit(1)

def create_mock_ticket_manager(client, config):
    """Create a mock ticket manager for testing"""
    
    class MockTicketManager:
        def __init__(self, api_client, config):
            self.api_client = api_client
            self.config = config
            self.logger = logging.getLogger(__name__)
            self.ticket_counter = 1000
            
        def create_recertification_ticket(self, rule_info, assignee=None, custom_template=None):
            """Create a mock recertification ticket"""
            self.ticket_counter += 1
            ticket_id = f"MOCK-RECERT-{self.ticket_counter}"
            
            # Use template from config
            template = custom_template or self.config.get('ticket_template', {})
            
            # Create format parameters without conflicts
            format_params = {
                'rule_name': rule_info.get('rule_name', 'Unknown'),
                'days_until_expiration': rule_info.get('days_until_expiration', 0),
                'device_name': rule_info.get('device_name', 'Unknown'),
                'rule_id': rule_info.get('rule_id', 'Unknown'),
                'rule_number': rule_info.get('rule_number', 'Unknown'),
                'expiration_date': rule_info.get('expiration_date', 'Unknown'),
                'expiration_type': rule_info.get('expiration_type', 'Unknown'),
                'rule_owner': rule_info.get('rule_owner', 'Unknown')
            }

            subject = template.get('subject_template', 'Rule Recertification Required').format(**format_params)
            description = template.get('description_template', 'Rule requires recertification').format(**format_params)
            
            self.logger.info(f"Mock ticket created: {ticket_id} for rule {rule_info.get('rule_id')}")
            
            return {
                'success': True,
                'ticket_id': ticket_id,
                'ticket_url': f"https://mock-tufin.local/tickets/{ticket_id}",
                'ticket_data': {
                    'id': ticket_id,
                    'subject': subject,
                    'description': description,
                    'assignee': assignee,
                    'priority': template.get('priority', 'medium'),
                    'status': 'open',
                    'created_date': datetime.now().isoformat()
                },
                'rule_info': rule_info
            }
        
        def bulk_create_recertification_tickets(self, rules_list, auto_assign=True):
            """Create multiple mock tickets"""
            results = {
                'total_rules': len(rules_list),
                'successful_tickets': [],
                'failed_tickets': [],
                'assignment_results': []
            }
            
            for rule_info in rules_list:
                assignee = rule_info.get('validated_owner') if auto_assign else None
                ticket_result = self.create_recertification_ticket(rule_info, assignee)
                
                if ticket_result['success']:
                    results['successful_tickets'].append(ticket_result)
                    
                    if assignee:
                        assignment_result = {
                            'success': True,
                            'ticket_id': ticket_result['ticket_id'],
                            'assignee': assignee,
                            'assignment_method': 'automatic'
                        }
                        results['assignment_results'].append(assignment_result)
                else:
                    results['failed_tickets'].append({
                        'rule_info': rule_info,
                        'error': ticket_result.get('error', 'Unknown error')
                    })
            
            return results
    
    return MockTicketManager(client, config)

def run_complete_test():
    """Run the complete recertification test"""
    print("🚀 Starting Tufin Rule Recertification Local Test")
    print("="*60)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Load configuration
    config = load_test_config()
    logger.info("Configuration loaded successfully")
    
    # Initialize mock API client
    print("📡 Initializing Mock Tufin API Client...")
    client = MockTufinAPIClient(
        base_url=config['tufin']['base_url'],
        username=config['tufin']['username'],
        password=config['tufin']['password']
    )
    
    # Test connection
    connection_result = client.test_connection()
    if connection_result['success']:
        print(f"✅ {connection_result['message']}")
    else:
        print(f"❌ Connection failed: {connection_result['message']}")
        return
    
    # Get expiring rules
    print(f"\n🔍 Searching for rules expiring in next {config['recertification']['days_ahead']} days...")
    expiring_rules = client.get_rules_with_expiration(
        days_ahead=config['recertification']['days_ahead']
    )
    
    if not expiring_rules:
        print("ℹ️  No expiring rules found in dummy data")
        return
    
    print(f"⚠️  Found {len(expiring_rules)} expiring rules")
    
    # Initialize owner manager
    print(f"\n👥 Initializing Rule Owner Manager...")
    owner_manager = RuleOwnerManager(
        client,
        default_owner=config['recertification']['default_owner'],
        owner_mapping=config.get('owner_mapping', {})
    )
    
    # Perform bulk owner validation
    print(f"🔍 Performing bulk owner validation...")
    validation_results = owner_manager.bulk_validate_owners(expiring_rules)
    
    summary = validation_results['validation_summary']
    print(f"📊 Validation Results:")
    print(f"   Rules with valid owners: {summary['rules_with_owners']}")
    print(f"   Rules without owners: {summary['rules_without_owners']}")
    print(f"   Unique owners identified: {summary['unique_owners']}")
    
    # Show owner distribution
    print(f"\n👥 Owner Distribution:")
    for owner, count in summary['owner_distribution'].items():
        print(f"   {owner}: {count} rules")
    
    # Create mock tickets
    print(f"\n🎫 Creating Mock Recertification Tickets...")
    ticket_manager = create_mock_ticket_manager(client, config)
    
    rules_with_owners = validation_results['rules_with_valid_owners']
    
    if rules_with_owners:
        ticket_results = ticket_manager.bulk_create_recertification_tickets(
            rules_with_owners,
            auto_assign=config['recertification']['auto_assign_tickets']
        )
        
        print(f"✅ Successfully created {len(ticket_results['successful_tickets'])} mock tickets")
        print(f"📌 Successfully assigned {len(ticket_results['assignment_results'])} tickets")
        
        # Show created tickets
        print(f"\n🎫 Created Tickets:")
        for ticket in ticket_results['successful_tickets']:
            rule_info = ticket['rule_info']
            ticket_data = ticket['ticket_data']
            assignee = ticket_data.get('assignee', 'Unassigned')
            print(f"   {ticket['ticket_id']}: {rule_info['rule_name']} → {assignee}")
    
    # Handle rules without owners
    rules_without_owners = validation_results['rules_without_owners']
    if rules_without_owners:
        print(f"\n❓ Rules without valid owners ({len(rules_without_owners)}):")
        for rule in rules_without_owners:
            print(f"   - {rule['rule_name']} on {rule['device_name']}")
        
        if not config['recertification']['only_create_tickets_with_owners']:
            print(f"   Creating tickets with default owner: {config['recertification']['default_owner']}")
            
            # Assign default owner and create tickets
            for rule in rules_without_owners:
                rule['validated_owner'] = config['recertification']['default_owner']
            
            default_ticket_results = ticket_manager.bulk_create_recertification_tickets(
                rules_without_owners,
                auto_assign=True
            )
            
            print(f"   ✅ Created {len(default_ticket_results['successful_tickets'])} tickets with default owner")
    
    # Generate summary report
    print(f"\n" + "="*60)
    print("📋 FINAL SUMMARY REPORT")
    print("="*60)
    
    total_rules = len(expiring_rules)
    rules_with_owners_count = len(rules_with_owners)
    rules_without_owners_count = len(rules_without_owners)
    
    print(f"📊 Overall Statistics:")
    print(f"   Total expiring rules: {total_rules}")
    print(f"   Rules with identified owners: {rules_with_owners_count} ({rules_with_owners_count/total_rules*100:.1f}%)")
    print(f"   Rules without owners: {rules_without_owners_count} ({rules_without_owners_count/total_rules*100:.1f}%)")
    
    print(f"\n🎯 Owner Detection Methods:")
    explicit_count = sum(1 for rule in expiring_rules if rule.get('rule_owner', '').strip())
    mapped_count = rules_with_owners_count - explicit_count
    print(f"   Explicit owners: {explicit_count}")
    print(f"   Pattern/mapping detected: {mapped_count}")
    print(f"   Default owner assigned: {rules_without_owners_count}")
    
    print(f"\n✅ Test completed successfully!")
    print(f"📝 Detailed logs saved to: local_test.log")
    
    # Save detailed results
    results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    detailed_results = {
        'test_timestamp': datetime.now().isoformat(),
        'configuration': config,
        'expiring_rules': expiring_rules,
        'validation_results': validation_results,
        'summary': {
            'total_rules': total_rules,
            'rules_with_owners': rules_with_owners_count,
            'rules_without_owners': rules_without_owners_count,
            'owner_detection_rate': rules_with_owners_count/total_rules*100 if total_rules > 0 else 0
        }
    }
    
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2, default=str)
    
    print(f"💾 Detailed results saved to: {results_file}")

def main():
    """Main entry point"""
    try:
        run_complete_test()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
