"""
SecureChange Ticket Manager for Tufin Rule Recertification

This module handles the creation and management of SecureChange tickets
for rule recertification processes.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from tufin_api_client import TufinAPIClient, TufinAPIError

class SecureChangeTicketManager:
    """
    Manager for SecureChange recertification tickets
    
    This class handles the creation, assignment, and tracking of
    rule recertification tickets in Tufin SecureChange.
    """
    
    def __init__(self, api_client: TufinAPIClient):
        """
        Initialize the ticket manager
        
        Args:
            api_client: Authenticated TufinAPIClient instance
        """
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        
        # Default ticket templates
        self.default_ticket_template = {
            "workflow": "rule_recertification",
            "priority": "medium",
            "subject_template": "Rule Recertification Required: {rule_name} on {device_name}",
            "description_template": """
Rule Recertification Required

Device: {device_name}
Rule ID: {rule_id}
Rule Name: {rule_name}
Rule Number: {rule_number}
Expiration Type: {expiration_type}
Expiration Date: {expiration_date}
Days Until Expiration: {days_until_expiration}

This rule requires recertification before its expiration date.
Please review the rule and confirm it is still required.

Actions Required:
1. Review the rule configuration
2. Verify the rule is still necessary
3. Update the rule if needed
4. Certify the rule for continued use

If the rule is no longer needed, please disable or remove it.
            """.strip()
        }
    
    def create_recertification_ticket(self, rule_info: Dict[str, Any], 
                                    assignee: Optional[str] = None,
                                    custom_template: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Create a recertification ticket for a specific rule
        
        Args:
            rule_info: Rule information from expiration checker
            assignee: User to assign the ticket to (optional)
            custom_template: Custom ticket template (optional)
            
        Returns:
            Dict: Created ticket information
        """
        try:
            # Use custom template or default
            template = custom_template or self.default_ticket_template
            
            # Format ticket subject and description
            subject = template["subject_template"].format(**rule_info)
            description = template["description_template"].format(**rule_info)
            
            # Prepare ticket data
            ticket_data = {
                "workflow": template.get("workflow", "rule_recertification"),
                "priority": template.get("priority", "medium"),
                "subject": subject,
                "description": description,
                "requester": "system",  # System-generated ticket
                "metadata": {
                    "rule_id": rule_info.get("rule_id"),
                    "device_id": rule_info.get("device_id"),
                    "expiration_type": rule_info.get("expiration_type"),
                    "expiration_date": rule_info.get("expiration_date").isoformat() if rule_info.get("expiration_date") else None,
                    "auto_generated": True,
                    "created_by_script": True
                }
            }
            
            # Add assignee if provided
            if assignee:
                ticket_data["assignee"] = assignee
            
            # Create the ticket via API
            url = f"{self.api_client.securechange_api}/tickets"
            response = self.api_client._make_request('POST', url, json=ticket_data)
            
            ticket_result = response.json()
            ticket_id = ticket_result.get('id')
            
            self.logger.info(f"Created recertification ticket {ticket_id} for rule {rule_info.get('rule_id')}")
            
            return {
                'success': True,
                'ticket_id': ticket_id,
                'ticket_url': f"{self.api_client.base_url}/securechange/tickets/{ticket_id}",
                'ticket_data': ticket_result,
                'rule_info': rule_info
            }
            
        except Exception as e:
            self.logger.error(f"Failed to create recertification ticket: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'rule_info': rule_info
            }
    
    def assign_ticket_to_owner(self, ticket_id: str, owner: str) -> Dict[str, Any]:
        """
        Assign a ticket to a specific owner
        
        Args:
            ticket_id: ID of the ticket to assign
            owner: Username or email of the assignee
            
        Returns:
            Dict: Assignment result
        """
        try:
            assignment_data = {
                "assignee": owner,
                "assigned_by": "system",
                "assignment_reason": "Automatic assignment to rule owner"
            }
            
            url = f"{self.api_client.securechange_api}/tickets/{ticket_id}/assign"
            response = self.api_client._make_request('PUT', url, json=assignment_data)
            
            self.logger.info(f"Assigned ticket {ticket_id} to {owner}")
            
            return {
                'success': True,
                'ticket_id': ticket_id,
                'assignee': owner,
                'assignment_data': response.json()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to assign ticket {ticket_id} to {owner}: {str(e)}")
            return {
                'success': False,
                'ticket_id': ticket_id,
                'assignee': owner,
                'error': str(e)
            }
    
    def get_ticket_status(self, ticket_id: str) -> Dict[str, Any]:
        """
        Get the current status of a ticket
        
        Args:
            ticket_id: ID of the ticket
            
        Returns:
            Dict: Ticket status information
        """
        try:
            url = f"{self.api_client.securechange_api}/tickets/{ticket_id}"
            response = self.api_client._make_request('GET', url)
            
            return {
                'success': True,
                'ticket_data': response.json()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get ticket status for {ticket_id}: {str(e)}")
            return {
                'success': False,
                'ticket_id': ticket_id,
                'error': str(e)
            }
    
    def bulk_create_recertification_tickets(self, rules_list: List[Dict[str, Any]], 
                                          auto_assign: bool = True) -> Dict[str, Any]:
        """
        Create recertification tickets for multiple rules
        
        Args:
            rules_list: List of rule information from expiration checker
            auto_assign: Whether to automatically assign tickets to rule owners
            
        Returns:
            Dict: Bulk creation results
        """
        results = {
            'total_rules': len(rules_list),
            'successful_tickets': [],
            'failed_tickets': [],
            'assignment_results': []
        }
        
        self.logger.info(f"Creating recertification tickets for {len(rules_list)} rules")
        
        for rule_info in rules_list:
            # Create the ticket
            ticket_result = self.create_recertification_ticket(rule_info)
            
            if ticket_result['success']:
                results['successful_tickets'].append(ticket_result)
                
                # Auto-assign if requested and owner is available
                if auto_assign and rule_info.get('rule_owner'):
                    assignment_result = self.assign_ticket_to_owner(
                        ticket_result['ticket_id'],
                        rule_info['rule_owner']
                    )
                    results['assignment_results'].append(assignment_result)
            else:
                results['failed_tickets'].append(ticket_result)
        
        # Summary logging
        success_count = len(results['successful_tickets'])
        failed_count = len(results['failed_tickets'])
        assigned_count = len([r for r in results['assignment_results'] if r['success']])
        
        self.logger.info(f"Ticket creation summary: {success_count} successful, {failed_count} failed")
        if auto_assign:
            self.logger.info(f"Assignment summary: {assigned_count} successfully assigned")
        
        return results
    
    def get_open_recertification_tickets(self) -> List[Dict[str, Any]]:
        """
        Get all open recertification tickets
        
        Returns:
            List[Dict]: List of open recertification tickets
        """
        try:
            url = f"{self.api_client.securechange_api}/tickets"
            params = {
                'status': 'open',
                'workflow': 'rule_recertification',
                'auto_generated': 'true'
            }
            
            response = self.api_client._make_request('GET', url, params=params)
            tickets = response.json().get('tickets', [])
            
            self.logger.info(f"Found {len(tickets)} open recertification tickets")
            return tickets
            
        except Exception as e:
            self.logger.error(f"Failed to get open recertification tickets: {str(e)}")
            return []
