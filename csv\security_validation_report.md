# 🔒 CSV Security Validation Report

**Generated:** 2025-07-26 11:42:48
**CSV File:** `test_comma_expansion.csv`
**Validation Engine:** CSV Security Validator v1.0

---

## 📊 Executive Summary

### Overall Security Status: 🔴 **CRITICAL**
*Immediate action required*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | 38 | ℹ️ |
| **Flows with Issues** | 38 | 🔴 |
| **Compliance Rate** | 0.0% | 🔴 |
| **Critical Issues** | 37 | 🔴 |
| **High Risk Issues** | 29 | 🟠 |
| **Medium Risk Issues** | 48 | 🟡 |
| **Low Risk Issues** | 0 | ⚪ |

## 🔄 Flow Expansion Analysis

| Metric | Value | Details |
|--------|-------|---------|
| **Original CSV Rows** | 3 | Rows in source CSV |
| **Total Flows Analyzed** | 38 | After comma-separated expansion |
| **Flows from Expansion** | 38 | Created from comma-separated values |
| **Expansion Groups** | 3 | CSV rows that were expanded |

### 🔄 Expansion Details
- **flow_1**: 8 flows (flow_1_a, flow_1_b, flow_1_c, flow_1_d, flow_1_e, flow_1_f, flow_1_g, flow_1_h)
- **flow_2**: 18 flows (flow_2_a, flow_2_b, flow_2_c, flow_2_d, flow_2_e, flow_2_f, flow_2_g, flow_2_h, flow_2_i, flow_2_j, flow_2_k, flow_2_l, flow_2_m, flow_2_n, flow_2_o, flow_2_p, flow_2_q, flow_2_r)
- **flow_3**: 12 flows (flow_3_a, flow_3_b, flow_3_c, flow_3_d, flow_3_e, flow_3_f, flow_3_g, flow_3_h, flow_3_i, flow_3_j, flow_3_k, flow_3_l)

## 🎯 Risk Level Breakdown

```
Critical:  37 issues  ████████████████████
High:      29 issues  ████████████████████
Medium:    48 issues  ████████████████████
Low:        0 issues  
```

## 📋 Summary
- ℹ️ Analyzed 38 flows, 38 flows have security issues
- 🔴 🔴 CRITICAL: 37 critical security issues found
- 🟠 🟠 HIGH: 29 high-risk issues found
- 🟡 🟡 MEDIUM: 48 medium-risk issues found

## 🔍 Detailed Findings

### 🔴 Critical Risk Issues

#### flow_2_a *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `22` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Port**: Port '22' is classified as risky
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_c *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `22` |
| Service | `http` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Port**: Port '22' is classified as risky
- 🟡 **Service**: Service 'http' is classified as deprecated
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_e *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `22` |
| Service | `https` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Port**: Port '22' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_g *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `80` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_i *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `80` |
| Service | `http` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟡 **Service**: Service 'http' is classified as deprecated
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_k *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `80` |
| Service | `https` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_m *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_o *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `http` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟡 **Service**: Service 'http' is classified as deprecated
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_2_q *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `exe` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'exe' is classified as blocked

#### flow_3_a *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_3_b *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

#### flow_3_c *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `ps1` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟡 **File Type**: File Type 'ps1' is classified as concerning

#### flow_3_d *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_3_e *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

#### flow_3_f *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `ps1` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟡 **File Type**: File Type 'ps1' is classified as concerning

#### flow_3_g *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********.` |
| Destination IP | `***********0` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_3_h *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********.` |
| Destination IP | `***********0` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

#### flow_3_i *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********.` |
| Destination IP | `***********0` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `ps1` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟡 **File Type**: File Type 'ps1' is classified as concerning

#### flow_3_j *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********.` |
| Destination IP | `***************` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `bat` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🔴 **File Type**: File Type 'bat' is classified as blocked

#### flow_3_k *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********.` |
| Destination IP | `***************` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `vbs` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'vbs' is classified as risky

#### flow_3_l *(expanded from flow_3)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********.` |
| Destination IP | `***************` |
| Port | `21` |
| Service | `ftp` |
| Action | `Allow` |
| File Type | `ps1` |

**Security Issues:**
- 🔴 **Port**: Port '21' is classified as blocked
- 🔴 **Service**: Service 'ftp' is classified as blocked
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟡 **File Type**: File Type 'ps1' is classified as concerning

### 🟠 High Risk Issues

#### flow_1_b *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_1_d *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `443` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_1_f *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_1_h *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `443` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_2_b *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `22` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟠 **Port**: Port '22' is classified as risky
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_d *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `22` |
| Service | `http` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟠 **Port**: Port '22' is classified as risky
- 🟡 **Service**: Service 'http' is classified as deprecated
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_f *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `22` |
| Service | `https` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟠 **Port**: Port '22' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_h *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `80` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_j *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `80` |
| Service | `http` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟡 **Service**: Service 'http' is classified as deprecated
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_l *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `80` |
| Service | `https` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_n *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `ssh` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_p *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `http` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟡 **Service**: Service 'http' is classified as deprecated
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

#### flow_2_r *(expanded from flow_2)*

**Expansion Context:** This flow was created from comma-separated values in: Port, Service, File Type

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `dll` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning
- 🟠 **File Type**: File Type 'dll' is classified as risky

### 🟡 Medium Risk Issues

#### flow_1_a *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_1_c *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_1_e *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_1_g *(expanded from flow_1)*

**Expansion Context:** This flow was created from comma-separated values in: Source IP, Destination IP, Service

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***************` |
| Port | `443` |
| Service | `https` |
| Action | `Allow` |
| File Type | `pdf` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning


---
*Report generated by CSV Security Validator - 2025-07-26 11:42:48*
