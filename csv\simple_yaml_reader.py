#!/usr/bin/env python3
"""
Simple YAML Reader - Basic examples for loading and processing YAML data.
Perfect for getting started with YAML processing.
"""

def load_yaml_simple(yaml_file: str) -> dict:
    """
    Load YAML data with automatic fallback.
    
    Args:
        yaml_file: Path to YAML file
        
    Returns:
        dict: Parsed YAML data
    """
    try:
        import yaml
        with open(yaml_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except ImportError:
        print("Note: Install PyYAML for better performance: pip install PyYAML")
        return load_yaml_manual(yaml_file)

def load_yaml_manual(yaml_file: str) -> dict:
    """Manual YAML parsing for simple structures."""
    data = {'flows': {}}
    current_flow = None
    
    with open(yaml_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if ':' in line and not line.startswith('#'):
                if line.startswith('  ') and current_flow:
                    # Flow property
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip().strip("'\"")
                    data['flows'][current_flow][key] = value
                elif not line.startswith(' ') and line.endswith(':'):
                    # New flow
                    current_flow = line.rstrip(':')
                    if current_flow not in ['metadata', 'flows']:
                        data['flows'][current_flow] = {}
    
    return data

# Example 1: Basic iteration
def example_1_basic_iteration(yaml_file: str):
    """Example 1: Basic iteration through all flows."""
    print("Example 1: Basic Iteration")
    print("-" * 30)
    
    data = load_yaml_simple(yaml_file)
    flows = data.get('flows', {})
    
    for flow_id, flow_data in flows.items():
        print(f"Flow: {flow_id}")
        print(f"  Source: {flow_data.get('source_ip', 'N/A')}")
        print(f"  Destination: {flow_data.get('destination_ip', 'N/A')}")
        print(f"  Service: {flow_data.get('service', 'N/A')}")
        print(f"  Action: {flow_data.get('action', 'N/A')}")
        print()

# Example 2: Find specific values
def example_2_find_values(yaml_file: str):
    """Example 2: Find flows with specific values."""
    print("Example 2: Find Specific Values")
    print("-" * 30)
    
    data = load_yaml_simple(yaml_file)
    flows = data.get('flows', {})
    
    # Find all SSH flows
    ssh_flows = []
    for flow_id, flow_data in flows.items():
        if flow_data.get('service', '').lower() == 'ssh':
            ssh_flows.append(flow_id)
    
    print(f"SSH flows found: {ssh_flows}")
    
    # Find all Allow actions
    allow_flows = []
    for flow_id, flow_data in flows.items():
        if flow_data.get('action', '').lower() == 'allow':
            allow_flows.append(flow_id)
    
    print(f"Allow flows found: {allow_flows}")
    print()

# Example 3: Extract specific field values
def example_3_extract_fields(yaml_file: str):
    """Example 3: Extract all values for specific fields."""
    print("Example 3: Extract Field Values")
    print("-" * 30)
    
    data = load_yaml_simple(yaml_file)
    flows = data.get('flows', {})
    
    # Get all unique services
    services = set()
    ports = set()
    source_ips = set()
    
    for flow_data in flows.values():
        if 'service' in flow_data:
            services.add(flow_data['service'])
        if 'port' in flow_data:
            ports.add(flow_data['port'])
        if 'source_ip' in flow_data:
            source_ips.add(flow_data['source_ip'])
    
    print(f"Unique services: {sorted(services)}")
    print(f"Unique ports: {sorted(ports)}")
    print(f"Unique source IPs: {sorted(source_ips)}")
    print()

# Example 4: Filter and process
def example_4_filter_process(yaml_file: str):
    """Example 4: Filter flows and process them."""
    print("Example 4: Filter and Process")
    print("-" * 30)
    
    data = load_yaml_simple(yaml_file)
    flows = data.get('flows', {})
    
    # Filter: Find flows on specific ports
    high_risk_ports = ['22', '23', '21']  # SSH, Telnet, FTP
    risky_flows = []
    
    for flow_id, flow_data in flows.items():
        port = flow_data.get('port', '')
        if port in high_risk_ports:
            risky_flows.append({
                'flow_id': flow_id,
                'port': port,
                'service': flow_data.get('service', 'unknown'),
                'source': flow_data.get('source_ip', 'unknown')
            })
    
    print("High-risk port flows:")
    for flow in risky_flows:
        print(f"  {flow['flow_id']}: {flow['service']} on port {flow['port']} from {flow['source']}")
    print()

# Example 5: Create summary report
def example_5_summary_report(yaml_file: str):
    """Example 5: Create a summary report."""
    print("Example 5: Summary Report")
    print("-" * 30)
    
    data = load_yaml_simple(yaml_file)
    flows = data.get('flows', {})
    
    # Count by service
    service_counts = {}
    action_counts = {}
    
    for flow_data in flows.values():
        service = flow_data.get('service', 'unknown')
        action = flow_data.get('action', 'unknown')
        
        service_counts[service] = service_counts.get(service, 0) + 1
        action_counts[action] = action_counts.get(action, 0) + 1
    
    print(f"Total flows: {len(flows)}")
    print("Services breakdown:")
    for service, count in sorted(service_counts.items()):
        print(f"  {service}: {count}")
    
    print("Actions breakdown:")
    for action, count in sorted(action_counts.items()):
        print(f"  {action}: {count}")
    print()

def main():
    """Run all examples."""
    yaml_file = "csv_data_export.yaml"
    
    print("🔍 YAML Processing Examples")
    print("=" * 50)
    
    try:
        # Run all examples
        example_1_basic_iteration(yaml_file)
        example_2_find_values(yaml_file)
        example_3_extract_fields(yaml_file)
        example_4_filter_process(yaml_file)
        example_5_summary_report(yaml_file)
        
        print("✅ All examples completed successfully!")
        
    except FileNotFoundError:
        print(f"❌ Error: YAML file '{yaml_file}' not found")
        print("Run 'python csv_to_yaml_example.py' first to generate the YAML file")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
