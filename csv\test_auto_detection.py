#!/usr/bin/env python3
"""
Test script for automatic table detection functionality.
Creates CSV files with varying amounts of header content to test auto-detection.
"""

from csv_processor import csv_to_dict_simple, find_data_table_start
from security_validator import SecurityValidator

def create_variable_header_csvs():
    """Create test CSV files with different amounts of header content."""
    
    # Test 1: Short header (5 rows)
    short_header = [
        "# Network Security Analysis",
        "# Date: 2024-01-15",
        "# Analyst: Security Team",
        "#",
        ",Source IP,Destination IP,Port,Service,Action,File Type",
        "Flow,***********,***********0,80,http,Allow,pdf",
        "Flow,***********,************,443,https,Allow,exe",
        "Flow,***********,***********0,22,ssh,Deny,zip"
    ]
    
    with open("short_header.csv", "w", encoding="utf-8") as f:
        for line in short_header:
            f.write(line + "\n")
    
    # Test 2: Medium header (15 rows)
    medium_header = [
        "# NETWORK FLOW SECURITY ANALYSIS REPORT",
        "# =====================================",
        "#",
        "# Report Details:",
        "# - Generated: 2024-01-15 14:30:00 UTC",
        "# - Period: Last 24 hours",
        "# - Scope: All network segments",
        "# - Filter: High-risk protocols and file types",
        "#",
        "# Summary Statistics:",
        "# - Total flows analyzed: 1,234",
        "# - Suspicious activities: 45",
        "# - Critical alerts: 12",
        "#",
        ",Source IP,Destination IP,Port,Service,Action,File Type",
        "Flow,************,************0,21,ftp,Allow,txt",
        "Flow,************,************0,23,telnet,Deny,exe",
        "Flow,************,************0,443,https,Allow,dll"
    ]
    
    with open("medium_header.csv", "w", encoding="utf-8") as f:
        for line in medium_header:
            f.write(line + "\n")
    
    # Test 3: Long header (50 rows)
    long_header = [
        "# COMPREHENSIVE NETWORK SECURITY ANALYSIS",
        "# ======================================",
        "#",
        "# EXECUTIVE SUMMARY",
        "# ---------------",
        "# This report provides a detailed analysis of network traffic",
        "# patterns and identifies potential security threats based on",
        "# file transfers, protocol usage, and connection patterns.",
        "#",
        "# METHODOLOGY",
        "# -----------",
        "# 1. Data Collection:",
        "#    - Network packet capture over 24-hour period",
        "#    - Deep packet inspection for file type detection",
        "#    - Protocol analysis and classification",
        "#",
        "# 2. Risk Assessment:",
        "#    - File type categorization (critical, high, medium, low)",
        "#    - Protocol security evaluation",
        "#    - Source/destination IP reputation checking",
        "#",
        "# 3. Threat Detection:",
        "#    - Executable file transfer monitoring",
        "#    - Suspicious protocol usage patterns",
        "#    - Anomalous connection behaviors",
        "#",
        "# CONFIGURATION PARAMETERS",
        "# ------------------------",
        "# Analysis Period: 2024-01-14 00:00:00 to 2024-01-15 00:00:00",
        "# Time Zone: UTC",
        "# Network Segments: 10.0.0.0/8, ***********/16, **********/12",
        "# Monitored Protocols: HTTP, HTTPS, FTP, SSH, Telnet, SMTP, DNS",
        "# File Type Detection: Enabled (signature-based)",
        "# IP Reputation: Enabled (threat intelligence feeds)",
        "#",
        "# RISK CATEGORIES",
        "# ---------------",
        "# CRITICAL: exe, bat, cmd, scr, pif, com",
        "# HIGH: dll, sys, vbs, js, jar, msi, iso, img, dmg, pkg",
        "# MEDIUM: zip, rar, 7z, tar, gz, ps1, sh, py, pl, rb",
        "# LOW: pdf, doc, txt, jpg, png, gif, mp3, mp4",
        "#",
        "# PROTOCOL RISK ASSESSMENT",
        "# ------------------------",
        "# SSH: High risk (potential lateral movement)",
        "# Telnet: Critical risk (unencrypted credentials)",
        "# FTP: High risk (unencrypted data transfer)",
        "# HTTP: Medium risk (unencrypted web traffic)",
        "# HTTPS: Low risk (encrypted web traffic)",
        "#",
        "# ANALYSIS RESULTS",
        "# ================",
        "#",
        ",Source IP,Destination IP,Port,Service,Action,File Type",
        "Flow,***********,***********0,22,ssh,Allow,key",
        "Flow,***********,***********0,21,ftp,Allow,exe",
        "Flow,***********,***********0,23,telnet,Deny,bat"
    ]
    
    with open("long_header.csv", "w", encoding="utf-8") as f:
        for line in long_header:
            f.write(line + "\n")
    
    # Test 4: No clear headers (edge case)
    no_headers = [
        "Some random text",
        "More random content",
        "Not really headers",
        "Flow,***********,***********0,80,http,Allow,pdf",
        "Flow,***********,************,443,https,Allow,exe"
    ]
    
    with open("no_headers.csv", "w", encoding="utf-8") as f:
        for line in no_headers:
            f.write(line + "\n")
    
    print("✅ Created test CSV files:")
    print("  • short_header.csv (5 header rows)")
    print("  • medium_header.csv (15 header rows)")
    print("  • long_header.csv (50 header rows)")
    print("  • no_headers.csv (edge case)")

def test_auto_detection():
    """Test automatic table detection on various CSV files."""
    
    print("🔍 Testing Automatic Table Detection")
    print("=" * 50)
    
    test_files = [
        ("short_header.csv", "Short header (5 rows)"),
        ("medium_header.csv", "Medium header (15 rows)"),
        ("long_header.csv", "Long header (50 rows)"),
        ("no_headers.csv", "No clear headers (edge case)")
    ]
    
    for csv_file, description in test_files:
        print(f"\n📁 Testing: {description}")
        print("-" * 40)
        
        try:
            # Test auto-detection
            header_row, headers = find_data_table_start(csv_file)
            
            if headers:
                print(f"✅ Auto-detection successful")
                
                # Load data with auto-detection
                flow_data = csv_to_dict_simple(csv_file, auto_detect=True)
                
                if flow_data:
                    print(f"✅ Loaded {len(flow_data)} flows")
                    
                    # Show sample data
                    sample_flow = next(iter(flow_data.values()))
                    print(f"📋 Sample flow: {sample_flow}")
                    
                    # Test security validation
                    validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
                    results = validator.validate_flow_data(flow_data)
                    
                    critical_issues = results.get("critical_issues", 0)
                    high_issues = results.get("high_risk_issues", 0)
                    
                    if critical_issues > 0 or high_issues > 0:
                        print(f"🚨 Security issues: {critical_issues} critical, {high_issues} high-risk")
                    else:
                        print(f"✅ No critical security issues detected")
                else:
                    print(f"❌ No flow data loaded")
            else:
                print(f"❌ Auto-detection failed")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_manual_vs_auto():
    """Compare manual skip_rows vs auto-detection."""
    
    print(f"\n🔄 Comparing Manual vs Auto-Detection")
    print("=" * 45)
    
    csv_file = "medium_header.csv"
    
    # Test manual mode with different skip values
    print(f"📋 Manual mode tests:")
    for skip_rows in [0, 10, 14, 15, 20]:
        try:
            flow_data = csv_to_dict_simple(csv_file, skip_rows=skip_rows, auto_detect=False)
            if flow_data:
                sample = next(iter(flow_data.values()))
                has_valid_headers = len([k for k in sample.keys() if k and not k.startswith('#')]) > 2
                print(f"  skip_rows={skip_rows}: {len(flow_data)} flows, valid headers: {has_valid_headers}")
            else:
                print(f"  skip_rows={skip_rows}: No data loaded")
        except:
            print(f"  skip_rows={skip_rows}: Error")
    
    # Test auto-detection
    print(f"\n🤖 Auto-detection test:")
    try:
        flow_data = csv_to_dict_simple(csv_file, auto_detect=True)
        if flow_data:
            sample = next(iter(flow_data.values()))
            print(f"  Auto-detect: {len(flow_data)} flows")
            print(f"  Headers: {list(sample.keys())}")
            print(f"  ✅ Auto-detection found the correct table structure!")
        else:
            print(f"  Auto-detect: No data loaded")
    except Exception as e:
        print(f"  Auto-detect: Error - {e}")

def main():
    """Main test function."""
    
    print("🚀 Automatic Table Detection Test Suite")
    print("=" * 70)
    
    # Create test files
    create_variable_header_csvs()
    
    # Test auto-detection
    test_auto_detection()
    
    # Compare manual vs auto
    test_manual_vs_auto()
    
    print(f"\n✅ All tests completed!")
    print(f"\n💡 Key Benefits of Auto-Detection:")
    print(f"  🔍 No need to count header rows manually")
    print(f"  📊 Works with any amount of metadata/instructions")
    print(f"  🔧 Automatically finds the data table")
    print(f"  📋 Handles varying CSV structures")
    print(f"  ⚡ Backward compatible with manual skip_rows")
    
    print(f"\n📖 Usage Examples:")
    print(f"  # Auto-detection (recommended)")
    print(f"  flow_data = csv_to_dict_simple('your_file.csv')")
    print(f"  ")
    print(f"  # Manual mode (if auto-detection fails)")
    print(f"  flow_data = csv_to_dict_simple('your_file.csv', skip_rows=31, auto_detect=False)")

if __name__ == "__main__":
    main()
