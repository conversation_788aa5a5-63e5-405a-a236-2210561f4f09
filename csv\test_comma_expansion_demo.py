#!/usr/bin/env python3
"""
Demo script to show comma-separated field expansion functionality.
"""

from security_validator import csv_to_dict_simple

def demo_comma_expansion():
    """Demonstrate the comma expansion feature."""
    
    print("🔄 Comma-Separated Field Expansion Demo")
    print("=" * 60)
    
    # Test with expansion enabled
    print("\n📊 With comma expansion ENABLED:")
    flow_data_expanded = csv_to_dict_simple("test_comma_expansion.csv", expand_comma_separated=True)
    
    print(f"Total flows: {len(flow_data_expanded)}")
    
    for flow_id, flow_data in flow_data_expanded.items():
        print(f"\n{flow_id}:")
        for field, value in flow_data.items():
            print(f"  {field}: {value}")
    
    # Test with expansion disabled
    print(f"\n" + "=" * 60)
    print("📊 With comma expansion DISABLED:")
    flow_data_original = csv_to_dict_simple("test_comma_expansion.csv", expand_comma_separated=False)
    
    print(f"Total flows: {len(flow_data_original)}")
    
    for flow_id, flow_data in flow_data_original.items():
        print(f"\n{flow_id}:")
        for field, value in flow_data.items():
            print(f"  {field}: {value}")

if __name__ == "__main__":
    demo_comma_expansion()
