#!/usr/bin/env python3
"""
Test script for comma-separated file type validation.
Tests the detection of multiple file types in a single field like 'exe,dll,zip'.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

def test_comma_separated_file_types():
    """Test comma-separated file type validation."""
    
    print("🔍 Testing Comma-Separated File Type Validation")
    print("=" * 60)
    
    # Load CSV with comma-separated file types
    csv_file = "Example_comma_separated_filetypes.csv"
    print(f"📁 Loading CSV file: {csv_file}")
    
    try:
        flow_data = csv_to_dict_simple(csv_file)
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Show what we loaded
        print(f"\n📋 Sample flow data:")
        for flow_id, flow_details in flow_data.items():
            file_type = flow_details.get("File Type", "N/A")
            print(f"  {flow_id}: File Type = '{file_type}'")
        
        # Run validation
        print(f"\n🔍 Running security validation...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        print(f"✅ Validation completed")
        print(f"📊 Results summary:")
        print(f"  • Total flows: {results['total_flows']}")
        print(f"  • Flows with issues: {results['flows_with_issues']}")
        print(f"  • Critical issues: {results['critical_issues']}")
        print(f"  • High risk issues: {results['high_risk_issues']}")
        print(f"  • Medium risk issues: {results['medium_risk_issues']}")
        
        # Detailed analysis of comma-separated file types
        print(f"\n🔍 Comma-Separated File Type Analysis:")
        print("=" * 60)
        
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type_field = flow_data_item.get("File Type", "N/A")
            issues = flow_result.get("issues", [])
            guidance_items = flow_result.get("guidance", [])
            
            print(f"\n📋 {flow_id}:")
            print(f"  Original File Type field: '{file_type_field}'")
            
            # Check if this has comma-separated values
            if "," in file_type_field:
                individual_types = [ft.strip() for ft in file_type_field.split(",")]
                print(f"  Individual file types detected: {individual_types}")
            else:
                print(f"  Single file type: {file_type_field}")
            
            # Show file type related issues
            file_type_issues = [issue for issue in issues if issue.get("field") == "File Type"]
            
            if file_type_issues:
                print(f"  🚨 File Type Issues Found:")
                for issue in file_type_issues:
                    risk_level = issue.get("risk_level", "unknown")
                    value = issue.get("value", "unknown")
                    category = issue.get("category", "unknown")
                    message = issue.get("message", "No message")
                    
                    risk_emoji = {
                        "critical": "🔴",
                        "high": "🟠", 
                        "medium": "🟡",
                        "low": "🟢"
                    }.get(risk_level, "⚪")
                    
                    print(f"    {risk_emoji} {value}: {risk_level.upper()} ({category})")
                    print(f"       {message}")
            else:
                print(f"  ✅ No file type issues detected")
            
            # Show guidance for file types
            file_type_guidance = [g for g in guidance_items if g.get("field") == "File Type"]
            
            if file_type_guidance:
                print(f"  📋 File Type Guidance:")
                for guidance in file_type_guidance:
                    value = guidance.get("value", "unknown")
                    message = guidance.get("message", "No message")
                    risk_level = guidance.get("risk_level", "unknown")
                    
                    print(f"    • {value}: {message} (Risk: {risk_level})")
                    
                    recommendations = guidance.get("recommendations", [])
                    if recommendations:
                        print(f"      Recommendations:")
                        for rec in recommendations[:2]:  # Show first 2 recommendations
                            print(f"        - {rec}")
        
        # Summary of detected file types across all flows
        print(f"\n📊 File Type Detection Summary:")
        print("=" * 40)
        
        all_detected_types = {}
        total_individual_files = 0
        
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type_field = flow_data_item.get("File Type", "")
            
            if file_type_field and file_type_field != "N/A":
                # Split by comma and process each type
                if "," in file_type_field:
                    individual_types = [ft.strip().lower() for ft in file_type_field.split(",") if ft.strip()]
                else:
                    individual_types = [file_type_field.strip().lower()]
                
                for file_type in individual_types:
                    if file_type:
                        total_individual_files += 1
                        if file_type not in all_detected_types:
                            all_detected_types[file_type] = {
                                "count": 0,
                                "flows": [],
                                "risk_level": "low"
                            }
                        
                        all_detected_types[file_type]["count"] += 1
                        all_detected_types[file_type]["flows"].append(flow_id)
                        
                        # Check risk level from issues
                        issues = flow_result.get("issues", [])
                        for issue in issues:
                            if (issue.get("field") == "File Type" and 
                                issue.get("value", "").lower() == file_type):
                                risk = issue.get("risk_level", "low")
                                current_risk = all_detected_types[file_type]["risk_level"]
                                
                                # Update to highest risk level
                                risk_order = {"low": 0, "medium": 1, "high": 2, "critical": 3}
                                if risk_order.get(risk, 0) > risk_order.get(current_risk, 0):
                                    all_detected_types[file_type]["risk_level"] = risk
        
        print(f"Total individual file types detected: {total_individual_files}")
        print(f"Unique file types: {len(all_detected_types)}")
        print(f"\nBreakdown by file type:")
        
        # Sort by risk level and count
        risk_order = {"critical": 3, "high": 2, "medium": 1, "low": 0}
        sorted_types = sorted(all_detected_types.items(), 
                            key=lambda x: (risk_order.get(x[1]["risk_level"], 0), x[1]["count"]), 
                            reverse=True)
        
        for file_type, info in sorted_types:
            count = info["count"]
            risk_level = info["risk_level"]
            flows = info["flows"]
            
            risk_emoji = {
                "critical": "🔴",
                "high": "🟠", 
                "medium": "🟡",
                "low": "🟢"
            }.get(risk_level, "⚪")
            
            print(f"  {risk_emoji} {file_type}: {count} occurrences, {risk_level} risk")
            print(f"     Found in: {', '.join(flows)}")
        
        return results
        
    except FileNotFoundError:
        print(f"❌ Error: CSV file '{csv_file}' not found")
        return None
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 Comma-Separated File Type Validation Test")
    print("=" * 70)
    
    results = test_comma_separated_file_types()
    
    if results:
        print(f"\n✅ Test completed successfully!")
        print(f"\n💡 Key Features Demonstrated:")
        print(f"  • Detection of individual file types in comma-separated lists")
        print(f"  • Risk assessment for each individual file type")
        print(f"  • Comprehensive guidance for each detected file type")
        print(f"  • Summary statistics across all flows")
    else:
        print(f"\n❌ Test failed!")
