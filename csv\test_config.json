{"tufin": {"base_url": "https://mock-tufin.local", "username": "test-user", "password": "test-password", "verify_ssl": false, "timeout": 30}, "recertification": {"days_ahead": 30, "auto_assign_tickets": true, "only_create_tickets_with_owners": false, "default_owner": "<EMAIL>"}, "owner_mapping": {"DMZ": "<EMAIL>", "WEB": "<EMAIL>", "DATABASE": "<EMAIL>", "DB": "<EMAIL>", "MAIL": "<EMAIL>", "CORE": "<EMAIL>", "VPN": "<EMAIL>", "BACKUP": "<EMAIL>", "MONITOR": "<EMAIL>"}, "owner_patterns": [{"pattern": ".*[Ww]eb.*", "owner": "<EMAIL>", "description": "Rules with 'web' in name or description"}, {"pattern": ".*[Dd]atabase.*|.*[Dd]b.*|.*[Mm]ysql.*|.*[Pp]ostgres.*", "owner": "<EMAIL>", "description": "Database-related rules"}, {"pattern": ".*[Mm]ail.*|.*[Ss]mtp.*|.*[Ii]map.*|.*[Pp]op.*", "owner": "<EMAIL>", "description": "Mail-related rules"}, {"pattern": ".*[Ss]sh.*|.*[Rr]dp.*|.*[Mm]anagement.*", "owner": "<EMAIL>", "description": "Management and remote access rules"}, {"pattern": ".*[Bb]ackup.*", "owner": "<EMAIL>", "description": "Backup-related rules"}, {"pattern": ".*[Mm]onitor.*|.*[Ss]nmp.*", "owner": "<EMAIL>", "description": "Monitoring-related rules"}, {"pattern": ".*[Vv]pn.*", "owner": "<EMAIL>", "description": "VPN-related rules"}], "ticket_template": {"workflow": "rule_recertification", "priority": "medium", "subject_template": "URGENT: Rule '{rule_name}' expires in {days_until_expiration} days", "description_template": "Rule '{rule_name}' on device '{device_name}' requires immediate recertification.\n\nRule Details:\n- Rule ID: {rule_id}\n- Rule Number: {rule_number}\n- Device: {device_name}\n- Expiration Date: {expiration_date}\n- Expiration Type: {expiration_type}\n- Days Until Expiration: {days_until_expiration}\n\nPlease review this rule and confirm it is still required. If the rule is no longer needed, please disable it. If it is still required, please update the expiration date.\n\nContact the Security Team if you have any questions.", "custom_fields": {"rule_id": "{rule_id}", "device_name": "{device_name}", "expiration_type": "{expiration_type}", "original_owner": "{rule_owner}"}}, "logging": {"log_level": "INFO", "log_file": "test_tufin_recertification.log", "console_output": true}, "testing": {"use_mock_client": true, "dummy_data_file": "dummy_tufin_data.json", "create_actual_tickets": false, "dry_run_mode": true}}