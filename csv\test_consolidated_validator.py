#!/usr/bin/env python3
"""
Test script for the consolidated SecurityValidator with built-in CSV validation.
"""

from security_validator import SecurityValidator

def test_consolidated_validator():
    """Test the consolidated validator functionality."""
    
    print("🧪 Testing Consolidated Security Validator")
    print("=" * 60)
    
    # Initialize the validator
    validator = SecurityValidator(
        unacceptable_values_file="unacceptable_values.json",
        guidance_file="security_guidance.json"
    )
    
    # Test files
    test_files = [
        "Example.csv",
        "Example_with_31_header_rows.csv"
    ]
    
    for csv_file in test_files:
        print(f"\n{'='*80}")
        print(f"🔍 Testing: {csv_file}")
        print(f"{'='*80}")
        
        try:
            # This single call handles everything:
            # 1. CSV loading and validation
            # 2. Structure checking
            # 3. Security analysis
            # 4. Report generation
            # 5. Results display
            results = validator.validate_csv_file(csv_file)
            
            print(f"\n📊 Final Results Summary:")
            print(f"   Success: {results.get('success', False)}")
            print(f"   Total flows: {results.get('total_flows', 0)}")
            print(f"   Critical issues: {results.get('critical_issues', 0)}")
            print(f"   High risk issues: {results.get('high_risk_issues', 0)}")
            
        except FileNotFoundError:
            print(f"❌ File not found: {csv_file}")
        except Exception as e:
            print(f"❌ Error processing {csv_file}: {e}")
    
    print(f"\n✅ Testing completed!")

if __name__ == "__main__":
    test_consolidated_validator()
