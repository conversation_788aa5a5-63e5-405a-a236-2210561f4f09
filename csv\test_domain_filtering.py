#!/usr/bin/env python3
"""
Test script to demonstrate domain filtering functionality
"""

import json
import sys
from mock_tufin_api_client import MockTufinAPIClient

def test_domain_filtering():
    """Test domain filtering functionality with mock data"""
    
    print("🧪 Testing Tufin Domain Filtering Functionality")
    print("=" * 60)
    
    # Initialize mock client
    client = MockTufinAPIClient("https://mock-tufin.company.com", "test", "test")
    
    # Test 1: List all domains
    print("\n1️⃣ Testing: List all available domains")
    print("-" * 40)
    domains = client.get_domains()
    for domain in domains:
        print(f"   Domain {domain['id']}: {domain['name']} - {domain['description']}")
    
    # Test 2: Get all devices (no filtering)
    print("\n2️⃣ Testing: Get all devices (no domain filter)")
    print("-" * 40)
    all_devices = client.get_devices()
    print(f"   Total devices: {len(all_devices)}")
    for device in all_devices:
        domain_id = device.get('domain_id', 'N/A')
        print(f"   • {device['name']} (ID: {device['id']}) - Domain {domain_id}")
    
    # Test 3: Get devices filtered by Domain 1
    print("\n3️⃣ Testing: Get devices filtered by Domain 1")
    print("-" * 40)
    domain1_devices = client.get_devices(domain_id=1)
    print(f"   Domain 1 devices: {len(domain1_devices)}")
    for device in domain1_devices:
        print(f"   • {device['name']} (ID: {device['id']}) - Domain {device.get('domain_id')}")
    
    # Test 4: Get devices filtered by Domain 2
    print("\n4️⃣ Testing: Get devices filtered by Domain 2")
    print("-" * 40)
    domain2_devices = client.get_devices(domain_id=2)
    print(f"   Domain 2 devices: {len(domain2_devices)}")
    for device in domain2_devices:
        print(f"   • {device['name']} (ID: {device['id']}) - Domain {device.get('domain_id')}")
    
    # Test 5: Get expiring rules for all domains
    print("\n5️⃣ Testing: Get expiring rules (all domains)")
    print("-" * 40)
    all_rules = client.get_rules_with_expiration(days_ahead=30)
    print(f"   Total expiring rules: {len(all_rules)}")
    
    # Group by domain
    domain_rules = {}
    for rule in all_rules:
        device_id = rule['device_id']
        # Find device domain
        device = next((d for d in all_devices if d['id'] == device_id), None)
        if device:
            domain_id = device.get('domain_id', 'Unknown')
            if domain_id not in domain_rules:
                domain_rules[domain_id] = []
            domain_rules[domain_id].append(rule)
    
    for domain_id, rules in domain_rules.items():
        print(f"   Domain {domain_id}: {len(rules)} expiring rules")
    
    # Test 6: Get expiring rules filtered by Domain 1
    print("\n6️⃣ Testing: Get expiring rules (Domain 1 only)")
    print("-" * 40)
    domain1_rules = client.get_rules_with_expiration(days_ahead=30, domain_id=1)
    print(f"   Domain 1 expiring rules: {len(domain1_rules)}")
    for rule in domain1_rules[:3]:  # Show first 3 rules
        print(f"   • {rule['rule_name']} on {rule['device_name']} (expires: {rule['expiration_date'].strftime('%Y-%m-%d')})")
    if len(domain1_rules) > 3:
        print(f"   ... and {len(domain1_rules) - 3} more rules")
    
    # Test 7: Get expiring rules filtered by Domain 2
    print("\n7️⃣ Testing: Get expiring rules (Domain 2 only)")
    print("-" * 40)
    domain2_rules = client.get_rules_with_expiration(days_ahead=30, domain_id=2)
    print(f"   Domain 2 expiring rules: {len(domain2_rules)}")
    for rule in domain2_rules[:3]:  # Show first 3 rules
        print(f"   • {rule['rule_name']} on {rule['device_name']} (expires: {rule['expiration_date'].strftime('%Y-%m-%d')})")
    if len(domain2_rules) > 3:
        print(f"   ... and {len(domain2_rules) - 3} more rules")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DOMAIN FILTERING TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Total domains available: {len(domains)}")
    print(f"✅ Total devices: {len(all_devices)}")
    print(f"✅ Domain 1 devices: {len(domain1_devices)}")
    print(f"✅ Domain 2 devices: {len(domain2_devices)}")
    print(f"✅ Total expiring rules: {len(all_rules)}")
    print(f"✅ Domain 1 expiring rules: {len(domain1_rules)}")
    print(f"✅ Domain 2 expiring rules: {len(domain2_rules)}")
    
    print(f"\n💡 Filtering efficiency:")
    print(f"   Domain 1 reduces scope by {100 - (len(domain1_rules)/len(all_rules)*100):.1f}%")
    print(f"   Domain 2 reduces scope by {100 - (len(domain2_rules)/len(all_rules)*100):.1f}%")
    
    print(f"\n🎯 Usage examples:")
    print(f"   # List available domains:")
    print(f"   python tufin_rule_recertification.py --config config.json --list-domains")
    print(f"   ")
    print(f"   # Filter to Domain 1:")
    print(f"   python tufin_rule_recertification.py --config config.json --domain-id 1")
    print(f"   ")
    print(f"   # Or add to config file:")
    print(f'   "domain_filter": {{"enabled": true, "domain_id": 1}}')

def test_configuration_examples():
    """Show configuration examples for domain filtering"""
    
    print("\n" + "=" * 60)
    print("📝 CONFIGURATION EXAMPLES")
    print("=" * 60)
    
    # Example 1: Domain filtering enabled
    config_domain1 = {
        "tufin": {
            "base_url": "https://your-tufin-server.company.com",
            "username": "your-username",
            "password": "your-password"
        },
        "recertification": {
            "days_ahead": 30,
            "domain_filter": {
                "enabled": True,
                "domain_id": 1,
                "domain_name": "Domain 1"
            }
        }
    }
    
    print("\n1️⃣ Configuration for Domain 1 filtering:")
    print(json.dumps(config_domain1, indent=2))
    
    # Example 2: No domain filtering
    config_all = {
        "recertification": {
            "days_ahead": 30,
            "domain_filter": {
                "enabled": False
            }
        }
    }
    
    print("\n2️⃣ Configuration for all domains (no filtering):")
    print(json.dumps(config_all, indent=2))

if __name__ == "__main__":
    test_domain_filtering()
    test_configuration_examples()
