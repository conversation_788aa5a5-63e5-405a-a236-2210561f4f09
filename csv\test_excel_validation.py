#!/usr/bin/env python3
"""
Test script for Excel validation functionality.
"""

from excel_validator import ExcelValidator
import pandas as pd

def test_validation_logic():
    """Test the validation logic without requiring Excel files."""
    
    print("🧪 Testing Excel Validation Logic")
    print("=" * 60)
    
    validator = ExcelValidator()
    
    # Test individual field validation
    print("\n🔍 Testing individual field validation:")
    
    # Test IP address validation
    print("\n📍 IP Address Validation:")
    ip_errors = validator._validate_ip_address("Source IP", "***********")
    print(f"   Valid IP '***********': {len(ip_errors)} errors")
    
    ip_errors = validator._validate_ip_address("Source IP", "invalid_ip")
    print(f"   Invalid IP 'invalid_ip': {len(ip_errors)} errors - {ip_errors}")
    
    # Test port validation
    print("\n🔌 Port Validation:")
    port_errors = validator._validate_port_number("Port", "443", False)
    print(f"   Valid port '443': {len(port_errors)} errors")
    
    port_errors = validator._validate_port_number("Port", "22,80,443", True)
    print(f"   Valid comma-separated ports '22,80,443': {len(port_errors)} errors")
    
    port_errors = validator._validate_port_number("Port", "99999", False)
    print(f"   Invalid port '99999': {len(port_errors)} errors - {port_errors}")
    
    # Test service validation
    print("\n🌐 Service Validation:")
    rules = {"valid_values": ["http", "https", "ssh", "ftp"], "allow_comma_separated": True}
    service_errors = validator._validate_service_name("Service", "https", rules)
    print(f"   Valid service 'https': {len(service_errors)} errors")
    
    service_errors = validator._validate_service_name("Service", "ssh,http", rules)
    print(f"   Valid comma-separated services 'ssh,http': {len(service_errors)} errors")
    
    service_errors = validator._validate_service_name("Service", "invalid_service", rules)
    print(f"   Invalid service 'invalid_service': {len(service_errors)} errors - {service_errors}")
    
    # Test action validation
    print("\n⚡ Action Validation:")
    action_rules = {"valid_values": ["Allow", "Deny", "Block"]}
    action_errors = validator._validate_action("Action", "Allow", action_rules)
    print(f"   Valid action 'Allow': {len(action_errors)} errors")
    
    action_errors = validator._validate_action("Action", "Maybe", action_rules)
    print(f"   Invalid action 'Maybe': {len(action_errors)} errors - {action_errors}")

def test_csv_validation_integration():
    """Test how the Excel validator could integrate with CSV validation."""
    
    print(f"\n🔗 CSV Integration Test")
    print("=" * 40)
    
    # Create a mock DataFrame (simulating Excel data)
    data = {
        'Source IP': ['***********', '***********', 'invalid_ip'],
        'Destination IP': ['***********0', '************', '10.10.10.300'],
        'Port': ['443', '22,80', '99999'],
        'Service': ['https', 'ssh,http', 'invalid_service'],
        'Action': ['Allow', 'Allow', 'Maybe'],
        'File Type': ['pdf', 'exe,dll', 'bat@#$']
    }
    
    df = pd.DataFrame(data)
    print(f"📊 Mock Excel data with {len(df)} rows:")
    print(df.to_string(index=False))
    
    # Simulate validation
    validator = ExcelValidator()
    
    print(f"\n🔍 Simulating validation...")
    total_errors = 0
    
    for index, row in df.iterrows():
        row_number = index + 2  # Excel row number
        row_errors = []
        
        # Validate each field
        validation_rules = validator.validation_rules["validation_rules"]
        
        for column, rules in validation_rules.items():
            if column in df.columns:
                cell_value = row[column]
                field_errors = validator._validate_field(column, cell_value, rules, row_number)
                row_errors.extend(field_errors)
        
        if row_errors:
            print(f"\n❌ Row {row_number} errors:")
            for error in row_errors:
                print(f"   • {error}")
            total_errors += len(row_errors)
        else:
            print(f"✅ Row {row_number}: No errors")
    
    print(f"\n📊 Validation Summary:")
    print(f"   Total errors: {total_errors}")
    print(f"   Can save as CSV: {'✅ Yes' if total_errors == 0 else '❌ No'}")

def demonstrate_validation_rules():
    """Demonstrate the validation rules structure."""
    
    print(f"\n📋 Validation Rules Structure")
    print("=" * 50)
    
    validator = ExcelValidator()
    rules = validator.validation_rules
    
    print(f"Required columns: {rules['required_columns']}")
    print(f"Optional columns: {rules['optional_columns']}")
    
    print(f"\nField validation rules:")
    for field, field_rules in rules['validation_rules'].items():
        print(f"\n{field}:")
        for rule_name, rule_value in field_rules.items():
            print(f"  {rule_name}: {rule_value}")

if __name__ == "__main__":
    test_validation_logic()
    test_csv_validation_integration()
    demonstrate_validation_rules()
    
    print(f"\n💡 Usage Instructions:")
    print(f"1. Install required packages: pip install pandas openpyxl")
    print(f"2. Use ExcelValidator.validate_excel_file('file.xlsx') to validate")
    print(f"3. Use ExcelValidator.save_as_csv_if_valid('input.xlsx', 'output.csv') to convert")
    print(f"4. Validation prevents saving CSV if data quality issues exist")
