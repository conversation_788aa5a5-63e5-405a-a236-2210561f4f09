#!/usr/bin/env python3
"""
Test script for file type validation.
Tests the detection of file types like 'exe' in the File Type column.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

def test_file_type_validation():
    """Test file type validation with various file types."""
    
    print("🔍 Testing File Type Validation")
    print("=" * 50)
    
    # Load CSV with file types
    csv_file = "Example_with_filetypes.csv"
    print(f"📁 Loading CSV file: {csv_file}")
    
    try:
        flow_data = csv_to_dict_simple(csv_file)
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Show what we loaded
        print(f"\n📋 Sample flow data:")
        for flow_id, flow_details in list(flow_data.items())[:2]:
            print(f"  {flow_id}:")
            for key, value in flow_details.items():
                print(f"    {key}: {value}")
        
        # Run validation
        print(f"\n🔍 Running security validation...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        print(f"✅ Validation completed")
        print(f"📊 Results summary:")
        print(f"  • Total flows: {results['total_flows']}")
        print(f"  • Flows with issues: {results['flows_with_issues']}")
        print(f"  • Critical issues: {results['critical_issues']}")
        print(f"  • High risk issues: {results['high_risk_issues']}")
        print(f"  • Medium risk issues: {results['medium_risk_issues']}")
        
        # Check specific file type detections
        print(f"\n🔍 File Type Detection Results:")
        print("-" * 40)
        
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type = flow_data_item.get("File Type", "N/A")
            issues = flow_result.get("issues", [])
            
            print(f"\n📋 {flow_id} (File Type: {file_type}):")
            
            # Check for file type related issues
            file_type_issues = [issue for issue in issues if issue.get("field") == "File Type"]
            
            if file_type_issues:
                for issue in file_type_issues:
                    risk_level = issue.get("risk_level", "unknown")
                    message = issue.get("message", "No message")
                    category = issue.get("category", "unknown")
                    
                    risk_emoji = {
                        "critical": "🔴",
                        "high": "🟠", 
                        "medium": "🟡",
                        "low": "🟢"
                    }.get(risk_level, "⚪")
                    
                    print(f"  {risk_emoji} {risk_level.upper()}: {message}")
                    print(f"    Category: {category}")
            else:
                print(f"  ✅ No file type issues detected")
            
            # Show guidance
            guidance_items = flow_result.get("guidance", [])
            file_type_guidance = [g for g in guidance_items if g.get("field") == "File Type"]
            
            if file_type_guidance:
                for guidance in file_type_guidance:
                    print(f"  📋 Guidance: {guidance.get('message', 'No message')}")
                    recommendations = guidance.get('recommendations', [])
                    if recommendations:
                        print(f"    Recommendations:")
                        for rec in recommendations:
                            print(f"      • {rec}")
        
        # Test specific file types
        print(f"\n🎯 Specific File Type Tests:")
        print("-" * 40)
        
        test_cases = [
            ("exe", "critical"),
            ("bat", "critical"), 
            ("dll", "high"),
            ("zip", "medium"),
            ("pdf", "low")
        ]
        
        for file_type, expected_risk in test_cases:
            found_flows = []
            for flow_id, flow_result in results["flow_results"].items():
                flow_data_item = flow_result.get("flow_data", {})
                if flow_data_item.get("File Type", "").lower() == file_type:
                    found_flows.append(flow_id)
                    
                    # Check if risk level matches expected
                    file_type_issues = [issue for issue in flow_result.get("issues", []) 
                                      if issue.get("field") == "File Type"]
                    
                    if file_type_issues:
                        actual_risk = file_type_issues[0].get("risk_level", "unknown")
                        status = "✅" if actual_risk == expected_risk else "❌"
                        print(f"  {status} {file_type}: Expected {expected_risk}, Got {actual_risk} ({flow_id})")
                    else:
                        print(f"  ⚪ {file_type}: No issues detected ({flow_id})")
            
            if not found_flows:
                print(f"  ⚪ {file_type}: No flows found with this file type")
        
        return results
        
    except FileNotFoundError:
        print(f"❌ Error: CSV file '{csv_file}' not found")
        return None
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_configuration():
    """Test that the configuration files are properly set up."""
    
    print(f"\n🔧 Testing Configuration")
    print("=" * 30)
    
    try:
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        
        # Check if File Type is in unacceptable values
        if "File Type" in validator.unacceptable_values:
            print(f"✅ File Type rules found in unacceptable_values.json")
            file_type_rules = validator.unacceptable_values["File Type"]
            for category, values in file_type_rules.items():
                print(f"  {category}: {values}")
        else:
            print(f"❌ File Type rules NOT found in unacceptable_values.json")
        
        # Check if file type guidance exists
        if "file_type_guidance" in validator.guidance:
            print(f"✅ File type guidance found in security_guidance.json")
            guidance_count = len(validator.guidance["file_type_guidance"])
            print(f"  {guidance_count} file types have guidance")
        else:
            print(f"❌ File type guidance NOT found in security_guidance.json")
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")

if __name__ == "__main__":
    print("🚀 File Type Validation Test Suite")
    print("=" * 60)
    
    # Test configuration
    test_configuration()
    
    # Test validation
    results = test_file_type_validation()
    
    if results:
        print(f"\n✅ All tests completed!")
        print(f"📁 Check the generated report: security_validation_report.md")
    else:
        print(f"\n❌ Tests failed!")
