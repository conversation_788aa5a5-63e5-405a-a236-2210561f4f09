#!/usr/bin/env python3
"""
Test script to verify the itertools fix works.
"""

from security_validator import expand_comma_separated_flows

def test_itertools_fix():
    """Test that the itertools fix handles different data types correctly."""
    
    print("🔧 Testing itertools Fix")
    print("=" * 40)
    
    # Test case 1: Normal case (should work)
    test_flow_1 = {
        "Source IP": "***********, ***********",
        "Destination IP": "************",
        "Port": "443",
        "Service": "https",
        "Action": "Allow",
        "File Type": "pdf"
    }
    
    print(f"\n1️⃣ Test Case 1: Normal comma-separated values")
    print(f"Input: {test_flow_1}")
    
    try:
        result = expand_comma_separated_flows(test_flow_1, "test_1")
        print(f"✅ Success! Generated {len(result)} flows")
        for i, flow in enumerate(result):
            print(f"   Flow {i+1}: {flow['Source IP']} → {flow['Destination IP']}")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test case 2: Multiple comma fields
    test_flow_2 = {
        "Source IP": "********, ********",
        "Destination IP": "********, ********",
        "Port": "22, 80",
        "Service": "ssh, http",
        "Action": "Allow",
        "File Type": "exe"
    }
    
    print(f"\n2️⃣ Test Case 2: Multiple comma-separated fields")
    print(f"Input: {test_flow_2}")
    
    try:
        result = expand_comma_separated_flows(test_flow_2, "test_2")
        print(f"✅ Success! Generated {len(result)} flows")
        for i, flow in enumerate(result[:3]):  # Show first 3
            print(f"   Flow {i+1}: {flow['Source IP']} → {flow['Destination IP']}:{flow['Port']} ({flow['Service']})")
        if len(result) > 3:
            print(f"   ... and {len(result) - 3} more flows")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test case 3: No commas (should return original)
    test_flow_3 = {
        "Source IP": "***********",
        "Destination IP": "************",
        "Port": "443",
        "Service": "https",
        "Action": "Allow",
        "File Type": "pdf"
    }
    
    print(f"\n3️⃣ Test Case 3: No comma-separated values")
    print(f"Input: {test_flow_3}")
    
    try:
        result = expand_comma_separated_flows(test_flow_3, "test_3")
        print(f"✅ Success! Generated {len(result)} flows (should be 1)")
        print(f"   Flow: {result[0]['Source IP']} → {result[0]['Destination IP']}")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_csv_loading():
    """Test CSV loading with the fix."""
    
    print(f"\n📁 Testing CSV Loading with Fix")
    print("=" * 40)
    
    try:
        from security_validator import csv_to_dict_simple
        
        flow_data = csv_to_dict_simple("test_comma_expansion.csv", expand_comma_separated=True)
        
        print(f"✅ CSV loaded successfully!")
        print(f"   Total flows: {len(flow_data)}")
        print(f"   Flow IDs: {list(flow_data.keys())[:5]}...")
        
        # Test a sample flow
        if flow_data:
            sample_id, sample_flow = list(flow_data.items())[0]
            print(f"   Sample flow: {sample_id}")
            for field, value in sample_flow.items():
                print(f"      {field}: '{value}'")
                
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        import traceback
        traceback.print_exc()

def test_full_validation():
    """Test the complete validation process."""
    
    print(f"\n🔍 Testing Full Validation Process")
    print("=" * 45)
    
    try:
        from security_validator import SecurityValidator
        
        validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')
        results = validator.validate_csv_file('test_comma_expansion.csv')
        
        print(f"✅ Full validation completed!")
        print(f"   Success: {results.get('success', True)}")
        print(f"   Total flows: {results.get('total_flows', 0)}")
        print(f"   Critical issues: {results.get('critical_issues', 0)}")
        print(f"   High risk issues: {results.get('high_risk_issues', 0)}")
        print(f"   Medium risk issues: {results.get('medium_risk_issues', 0)}")
        
        if results.get('errors'):
            print(f"   Errors: {results['errors']}")
            
    except Exception as e:
        print(f"❌ Error in full validation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Testing itertools Fix")
    print("=" * 50)
    
    # Test 1: Direct expansion function
    test_itertools_fix()
    
    # Test 2: CSV loading
    test_csv_loading()
    
    # Test 3: Full validation
    test_full_validation()
    
    print(f"\n💡 Summary:")
    print(f"   The fix ensures that comma_fields always contains proper lists")
    print(f"   Even if the data structure is corrupted, it will be converted")
    print(f"   This should resolve the 'list - list' error in itertools.product")
