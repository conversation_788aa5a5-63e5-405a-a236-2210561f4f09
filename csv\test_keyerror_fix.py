"""
Test script to verify the KeyError fix works.
"""

from security_validator import SecurityValidator
from csv_processor import csv_to_dict_simple


def test_keyerror_scenarios():
    """Test various scenarios that could cause KeyError."""
    print("=== TESTING KEYERROR FIX ===")
    
    # Initialize validator
    try:
        validator = SecurityValidator(
            unacceptable_values_file="unacceptable_values.json",
            guidance_file="security_guidance.json"
        )
        print("✅ Validator initialized successfully")
    except Exception as e:
        print(f"❌ Validator initialization failed: {e}")
        return False
    
    # Test 1: Empty flow data
    print("\nTest 1: Empty flow data")
    try:
        results = validator.validate_flow_data({})
        flows_with_issues = results["flows_with_issues"]
        print(f"✅ Empty data test passed: flows_with_issues = {flows_with_issues}")
    except KeyError as e:
        print(f"❌ KeyError with empty data: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error with empty data: {e}")
        return False
    
    # Test 2: None flow data
    print("\nTest 2: None flow data")
    try:
        results = validator.validate_flow_data(None)
        flows_with_issues = results["flows_with_issues"]
        print(f"✅ None data test passed: flows_with_issues = {flows_with_issues}")
    except KeyError as e:
        print(f"❌ KeyError with None data: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error with None data: {e}")
        return False
    
    # Test 3: Valid flow data
    print("\nTest 3: Valid flow data")
    try:
        test_data = {
            "flow_1": {
                "Service": "ssh",
                "Port": "22", 
                "Action": "Allow"
            }
        }
        results = validator.validate_flow_data(test_data)
        flows_with_issues = results["flows_with_issues"]
        print(f"✅ Valid data test passed: flows_with_issues = {flows_with_issues}")
    except KeyError as e:
        print(f"❌ KeyError with valid data: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error with valid data: {e}")
        return False
    
    # Test 4: Real CSV data
    print("\nTest 4: Real CSV data")
    try:
        flow_data = csv_to_dict_simple("Example.csv")
        results = validator.validate_flow_data(flow_data)
        flows_with_issues = results["flows_with_issues"]
        print(f"✅ Real CSV test passed: flows_with_issues = {flows_with_issues}")
        
        # Print detailed results
        print(f"  Total flows: {results['total_flows']}")
        print(f"  Critical issues: {results['critical_issues']}")
        print(f"  High risk issues: {results['high_risk_issues']}")
        print(f"  Medium risk issues: {results['medium_risk_issues']}")
        
    except KeyError as e:
        print(f"❌ KeyError with real CSV: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error with real CSV: {e}")
        return False
    
    print("\n✅ ALL TESTS PASSED - KeyError fix is working!")
    return True


def test_specific_keyerror():
    """Test the specific line that was causing the KeyError."""
    print("\n=== TESTING SPECIFIC KEYERROR LINE ===")
    
    # Create a minimal validation_results dict
    validation_results = {
        "total_flows": 0,
        "flows_with_issues": 0,
        "critical_issues": 0,
        "high_risk_issues": 0,
        "medium_risk_issues": 0,
        "low_risk_issues": 0,
        "flow_results": {},
        "summary": [],
        "errors": []
    }
    
    try:
        # This is the line that was causing the error
        validation_results["flows_with_issues"] += 1
        print(f"✅ Direct increment test passed: flows_with_issues = {validation_results['flows_with_issues']}")
        return True
    except KeyError as e:
        print(f"❌ KeyError on direct increment: {e}")
        print(f"Available keys: {list(validation_results.keys())}")
        return False


def main():
    """Run all tests."""
    print("KEYERROR FIX VERIFICATION")
    print("=" * 40)
    
    # Test the specific line first
    if not test_specific_keyerror():
        print("❌ Basic KeyError test failed!")
        return
    
    # Test various scenarios
    if not test_keyerror_scenarios():
        print("❌ Scenario tests failed!")
        return
    
    print("\n🎉 ALL KEYERROR TESTS PASSED!")
    print("The fix is working correctly.")


if __name__ == "__main__":
    main()
