# 🔒 CSV Security Validation Report

**Generated:** 2025-07-17 19:48:16
**CSV File:** `Example.csv`
**Validation Engine:** CSV Security Validator v1.0

---

## 📊 Executive Summary

### Overall Security Status: 🟠 **HIGH RISK**
*Significant security concerns identified*

### Key Metrics
| Metric | Value | Status |
|--------|-------|--------|
| **Total Flows Analyzed** | 4 | ℹ️ |
| **Flows with Issues** | 4 | 🔴 |
| **Compliance Rate** | 0.0% | 🔴 |
| **Critical Issues** | 0 | 🟢 |
| **High Risk Issues** | 1 | 🟠 |
| **Medium Risk Issues** | 4 | 🟡 |
| **Low Risk Issues** | 0 | ⚪ |

## 🔄 Flow Expansion Analysis

| Metric | Value | Details |
|--------|-------|---------|
| **Original CSV Rows** | 0 | Rows in source CSV |
| **Total Flows Analyzed** | 4 | After comma-separated expansion |
| **Flows from Expansion** | 0 | Created from comma-separated values |
| **Expansion Groups** | 0 | CSV rows that were expanded |


No comma-separated values were expanded.

## 🎯 Risk Level Breakdown

```
Critical:   0 issues  
High:       1 issues  █
Medium:     4 issues  ████
Low:        0 issues  
```

## 📋 Summary
- ℹ️ Analyzed 4 flows, 4 flows have security issues
- 🟠 🟠 HIGH: 1 high-risk issues found
- 🟡 🟡 MEDIUM: 4 medium-risk issues found

## 🔍 Detailed Findings

### 🟠 High Risk Issues

#### flow_4

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `444` |
| Service | `ssh` |
| Action | `Allow` |

**Security Issues:**
- 🟠 **Service**: Service 'ssh' is classified as risky
- 🟡 **Action**: Action 'Allow' is classified as concerning

### 🟡 Medium Risk Issues

#### flow_1

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `111` |
| Service | `https` |
| Action | `Allow` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_2

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `************` |
| Port | `222` |
| Service | `https` |
| Action | `Allow` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning

#### flow_3

**Flow Details:**
| Field | Value |
|-------|-------|
| Source IP | `***********` |
| Destination IP | `***********0` |
| Port | `333` |
| Service | `https` |
| Action | `Allow` |

**Security Issues:**
- 🟡 **Action**: Action 'Allow' is classified as concerning


---
*Report generated by CSV Security Validator - 2025-07-17 19:48:16*
