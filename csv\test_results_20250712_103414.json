{"test_timestamp": "2025-07-12T10:34:14.840067", "configuration": {"tufin": {"base_url": "https://mock-tufin.local", "username": "test-user", "password": "test-password", "verify_ssl": false, "timeout": 30}, "recertification": {"days_ahead": 30, "auto_assign_tickets": true, "only_create_tickets_with_owners": false, "default_owner": "<EMAIL>"}, "owner_mapping": {"DMZ": "<EMAIL>", "WEB": "<EMAIL>", "DATABASE": "<EMAIL>", "DB": "<EMAIL>", "MAIL": "<EMAIL>", "CORE": "<EMAIL>", "VPN": "<EMAIL>", "BACKUP": "<EMAIL>", "MONITOR": "<EMAIL>"}, "owner_patterns": [{"pattern": ".*[Ww]eb.*", "owner": "<EMAIL>", "description": "Rules with 'web' in name or description"}, {"pattern": ".*[Dd]atabase.*|.*[Dd]b.*|.*[Mm]ysql.*|.*[Pp]ostgres.*", "owner": "<EMAIL>", "description": "Database-related rules"}, {"pattern": ".*[Mm]ail.*|.*[Ss]mtp.*|.*[Ii]map.*|.*[Pp]op.*", "owner": "<EMAIL>", "description": "Mail-related rules"}, {"pattern": ".*[Ss]sh.*|.*[Rr]dp.*|.*[Mm]anagement.*", "owner": "<EMAIL>", "description": "Management and remote access rules"}, {"pattern": ".*[Bb]ackup.*", "owner": "<EMAIL>", "description": "Backup-related rules"}, {"pattern": ".*[Mm]onitor.*|.*[Ss]nmp.*", "owner": "<EMAIL>", "description": "Monitoring-related rules"}, {"pattern": ".*[Vv]pn.*", "owner": "<EMAIL>", "description": "VPN-related rules"}], "ticket_template": {"workflow": "rule_recertification", "priority": "medium", "subject_template": "URGENT: Rule '{rule_name}' expires in {days_until_expiration} days", "description_template": "Rule '{rule_name}' on device '{device_name}' requires immediate recertification.\n\nRule Details:\n- Rule ID: {rule_id}\n- Rule Number: {rule_number}\n- Device: {device_name}\n- Expiration Date: {expiration_date}\n- Expiration Type: {expiration_type}\n- Days Until Expiration: {days_until_expiration}\n\nPlease review this rule and confirm it is still required. If the rule is no longer needed, please disable it. If it is still required, please update the expiration date.\n\nContact the Security Team if you have any questions.", "custom_fields": {"rule_id": "{rule_id}", "device_name": "{device_name}", "expiration_type": "{expiration_type}", "original_owner": "{rule_owner}"}}, "logging": {"log_level": "INFO", "log_file": "test_tufin_recertification.log", "console_output": true}, "testing": {"use_mock_client": true, "dummy_data_file": "dummy_tufin_data.json", "create_actual_tickets": false, "dry_run_mode": true}}, "expiring_rules": [{"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-001", "rule_name": "DMZ Web Server Access", "rule_number": 1, "expiration_date": "2024-02-15 00:00:00", "expiration_type": "rule", "days_until_expiration": -514, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-001", "number": 1, "name": "DMZ Web Server Access", "enabled": true, "source": "Any", "destination": "DMZ_Web_Servers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "john.doe", "created_date": "2023-01-15", "last_modified": "2024-01-10", "expiration_date": "2024-02-15", "last_certified": "2023-02-15", "certification_expiry": "2024-02-15", "description": "Allow web traffic to DMZ web servers. Contact: <EMAIL> for changes.", "comments": "Annual recertification required", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-001", "rule_name": "DMZ Web Server Access", "rule_number": 1, "expiration_date": "2024-02-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -514, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-001", "number": 1, "name": "DMZ Web Server Access", "enabled": true, "source": "Any", "destination": "DMZ_Web_Servers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "john.doe", "created_date": "2023-01-15", "last_modified": "2024-01-10", "expiration_date": "2024-02-15", "last_certified": "2023-02-15", "certification_expiry": "2024-02-15", "description": "Allow web traffic to DMZ web servers. Contact: <EMAIL> for changes.", "comments": "Annual recertification required", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-002", "rule_name": "DMZ SSH Management", "rule_number": 2, "expiration_date": "2024-03-01 00:00:00", "expiration_type": "rule", "days_until_expiration": -499, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-002", "number": 2, "name": "DMZ SSH Management", "enabled": true, "source": "Management_Network", "destination": "DMZ_Servers", "service": "SSH", "action": "Allow", "owner": "<EMAIL>", "created_by": "admin", "created_date": "2023-03-01", "last_modified": "2024-01-05", "expiration_date": "2024-03-01", "last_certified": "2023-03-01", "certification_expiry": "2024-03-01", "description": "SSH access for DMZ server management", "comments": "Critical for operations - owner: <EMAIL>", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-002", "rule_name": "DMZ SSH Management", "rule_number": 2, "expiration_date": "2024-03-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -499, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-002", "number": 2, "name": "DMZ SSH Management", "enabled": true, "source": "Management_Network", "destination": "DMZ_Servers", "service": "SSH", "action": "Allow", "owner": "<EMAIL>", "created_by": "admin", "created_date": "2023-03-01", "last_modified": "2024-01-05", "expiration_date": "2024-03-01", "last_certified": "2023-03-01", "certification_expiry": "2024-03-01", "description": "SSH access for DMZ server management", "comments": "Critical for operations - owner: <EMAIL>", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-003", "rule_name": "Legacy FTP Access", "rule_number": 3, "expiration_date": "2024-01-30 00:00:00", "expiration_type": "rule", "days_until_expiration": -530, "rule_owner": "", "rule_data": {"id": "R1001-003", "number": 3, "name": "Legacy FTP Access", "enabled": true, "source": "Internal_Network", "destination": "DMZ_FTP_Server", "service": "FTP", "action": "Allow", "owner": "", "created_by": "legacy.user", "created_date": "2022-06-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-30", "last_certified": "", "certification_expiry": "", "description": "Legacy FTP access - needs review", "comments": "No clear owner identified", "risk_level": "critical"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-001", "rule_name": "WEB Load Balancer Access", "rule_number": 1, "expiration_date": "2024-02-20 00:00:00", "expiration_type": "rule", "days_until_expiration": -509, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-001", "number": 1, "name": "WEB Load Balancer Access", "enabled": true, "source": "Internet", "destination": "Web_Load_Balancers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "web.admin", "created_date": "2023-05-01", "last_modified": "2024-01-12", "expiration_date": "2024-02-20", "last_certified": "2023-05-01", "certification_expiry": "2024-05-01", "description": "Public web access through load balancers", "comments": "Business critical rule", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-001", "rule_name": "WEB Load Balancer Access", "rule_number": 1, "expiration_date": "2024-05-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -438, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-001", "number": 1, "name": "WEB Load Balancer Access", "enabled": true, "source": "Internet", "destination": "Web_Load_Balancers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "web.admin", "created_date": "2023-05-01", "last_modified": "2024-01-12", "expiration_date": "2024-02-20", "last_certified": "2023-05-01", "certification_expiry": "2024-05-01", "description": "Public web access through load balancers", "comments": "Business critical rule", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-002", "rule_name": "WEB Database Connection", "rule_number": 2, "expiration_date": "2024-02-10 00:00:00", "expiration_type": "rule", "days_until_expiration": -519, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-002", "number": 2, "name": "WEB Database Connection", "enabled": true, "source": "Web_Servers", "destination": "Database_Servers", "service": "MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "developer1", "created_date": "2023-04-15", "last_modified": "2024-01-08", "expiration_date": "2024-02-10", "last_certified": "2023-04-15", "certification_expiry": "2024-04-15", "description": "Web application database connectivity", "comments": "Application team responsible", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-002", "rule_name": "WEB Database Connection", "rule_number": 2, "expiration_date": "2024-04-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -454, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-002", "number": 2, "name": "WEB Database Connection", "enabled": true, "source": "Web_Servers", "destination": "Database_Servers", "service": "MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "developer1", "created_date": "2023-04-15", "last_modified": "2024-01-08", "expiration_date": "2024-02-10", "last_certified": "2023-04-15", "certification_expiry": "2024-04-15", "description": "Web application database connectivity", "comments": "Application team responsible", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-003", "rule_name": "WEB Monitoring Access", "rule_number": 3, "expiration_date": "2024-01-25 00:00:00", "expiration_type": "rule", "days_until_expiration": -535, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-003", "number": 3, "name": "WEB Monitoring Access", "enabled": true, "source": "Monitoring_Servers", "destination": "Web_Infrastructure", "service": "SNMP,HTTP", "action": "Allow", "owner": "<EMAIL>", "created_by": "monitor.admin", "created_date": "2023-07-01", "last_modified": "2023-12-15", "expiration_date": "2024-01-25", "last_certified": "2023-07-01", "certification_expiry": "2024-07-01", "description": "Infrastructure monitoring access", "comments": "Ops team maintains this rule", "risk_level": "low"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-003", "rule_name": "WEB Monitoring Access", "rule_number": 3, "expiration_date": "2024-07-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -377, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-003", "number": 3, "name": "WEB Monitoring Access", "enabled": true, "source": "Monitoring_Servers", "destination": "Web_Infrastructure", "service": "SNMP,HTTP", "action": "Allow", "owner": "<EMAIL>", "created_by": "monitor.admin", "created_date": "2023-07-01", "last_modified": "2023-12-15", "expiration_date": "2024-01-25", "last_certified": "2023-07-01", "certification_expiry": "2024-07-01", "description": "Infrastructure monitoring access", "comments": "Ops team maintains this rule", "risk_level": "low"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-001", "rule_name": "Database Replication", "rule_number": 1, "expiration_date": "2024-02-28 00:00:00", "expiration_type": "rule", "days_until_expiration": -501, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-001", "number": 1, "name": "Database Replication", "enabled": true, "source": "Primary_DB_Servers", "destination": "Secondary_DB_Servers", "service": "MySQL_Replication", "action": "Allow", "owner": "<EMAIL>", "created_by": "dba1", "created_date": "2023-02-01", "last_modified": "2024-01-03", "expiration_date": "2024-02-28", "last_certified": "2023-02-01", "certification_expiry": "2024-02-01", "description": "Database replication between primary and secondary servers", "comments": "Critical for DR - DBA team owns this", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-001", "rule_name": "Database Replication", "rule_number": 1, "expiration_date": "2024-02-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -528, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-001", "number": 1, "name": "Database Replication", "enabled": true, "source": "Primary_DB_Servers", "destination": "Secondary_DB_Servers", "service": "MySQL_Replication", "action": "Allow", "owner": "<EMAIL>", "created_by": "dba1", "created_date": "2023-02-01", "last_modified": "2024-01-03", "expiration_date": "2024-02-28", "last_certified": "2023-02-01", "certification_expiry": "2024-02-01", "description": "Database replication between primary and secondary servers", "comments": "Critical for DR - DBA team owns this", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-002", "rule_name": "Database Backup Access", "rule_number": 2, "expiration_date": "2024-01-31 00:00:00", "expiration_type": "rule", "days_until_expiration": -529, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-002", "number": 2, "name": "Database Backup Access", "enabled": true, "source": "Backup_Servers", "destination": "Database_Servers", "service": "Custom_Backup_Port", "action": "Allow", "owner": "<EMAIL>", "created_by": "backup.admin", "created_date": "2023-01-10", "last_modified": "2023-11-20", "expiration_date": "2024-01-31", "last_certified": "2023-01-10", "certification_expiry": "2024-01-10", "description": "Backup system access to databases", "comments": "Backup team responsible", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-002", "rule_name": "Database Backup Access", "rule_number": 2, "expiration_date": "2024-01-10 00:00:00", "expiration_type": "certification", "days_until_expiration": -550, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-002", "number": 2, "name": "Database Backup Access", "enabled": true, "source": "Backup_Servers", "destination": "Database_Servers", "service": "Custom_Backup_Port", "action": "Allow", "owner": "<EMAIL>", "created_by": "backup.admin", "created_date": "2023-01-10", "last_modified": "2023-11-20", "expiration_date": "2024-01-31", "last_certified": "2023-01-10", "certification_expiry": "2024-01-10", "description": "Backup system access to databases", "comments": "Backup team responsible", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-003", "rule_name": "Database Admin Access", "rule_number": 3, "expiration_date": "2024-03-15 00:00:00", "expiration_type": "rule", "days_until_expiration": -485, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-003", "number": 3, "name": "Database Admin Access", "enabled": true, "source": "DBA_Workstations", "destination": "All_Database_Servers", "service": "SSH,RDP,MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "senior.dba", "created_date": "2023-03-15", "last_modified": "2024-01-01", "expiration_date": "2024-03-15", "last_certified": "2023-03-15", "certification_expiry": "2024-03-15", "description": "DBA administrative access to all database servers", "comments": "Senior DBA approval required for changes", "risk_level": "critical"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-003", "rule_name": "Database Admin Access", "rule_number": 3, "expiration_date": "2024-03-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -485, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-003", "number": 3, "name": "Database Admin Access", "enabled": true, "source": "DBA_Workstations", "destination": "All_Database_Servers", "service": "SSH,RDP,MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "senior.dba", "created_date": "2023-03-15", "last_modified": "2024-01-01", "expiration_date": "2024-03-15", "last_certified": "2023-03-15", "certification_expiry": "2024-03-15", "description": "DBA administrative access to all database servers", "comments": "Senior DBA approval required for changes", "risk_level": "critical"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-001", "rule_name": "Core Network Routing", "rule_number": 1, "expiration_date": "2024-01-28 00:00:00", "expiration_type": "rule", "days_until_expiration": -532, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-001", "number": 1, "name": "Core Network Routing", "enabled": true, "source": "Internal_Networks", "destination": "Core_Infrastructure", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "network.admin", "created_date": "2023-01-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-28", "last_certified": "2023-01-01", "certification_expiry": "2024-01-01", "description": "Core network routing and connectivity", "comments": "Network team maintains - critical infrastructure", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-001", "rule_name": "Core Network Routing", "rule_number": 1, "expiration_date": "2024-01-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -559, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-001", "number": 1, "name": "Core Network Routing", "enabled": true, "source": "Internal_Networks", "destination": "Core_Infrastructure", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "network.admin", "created_date": "2023-01-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-28", "last_certified": "2023-01-01", "certification_expiry": "2024-01-01", "description": "Core network routing and connectivity", "comments": "Network team maintains - critical infrastructure", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-002", "rule_name": "VPN Access Rule", "rule_number": 2, "expiration_date": "2024-02-05 00:00:00", "expiration_type": "rule", "days_until_expiration": -524, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-002", "number": 2, "name": "VPN Access Rule", "enabled": true, "source": "VPN_Clients", "destination": "Internal_Resources", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "vpn.admin", "created_date": "2023-06-01", "last_modified": "2024-01-07", "expiration_date": "2024-02-05", "last_certified": "2023-06-01", "certification_expiry": "2024-06-01", "description": "VPN user access to internal resources", "comments": "VPN team manages user access", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-002", "rule_name": "VPN Access Rule", "rule_number": 2, "expiration_date": "2024-06-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -407, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-002", "number": 2, "name": "VPN Access Rule", "enabled": true, "source": "VPN_Clients", "destination": "Internal_Resources", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "vpn.admin", "created_date": "2023-06-01", "last_modified": "2024-01-07", "expiration_date": "2024-02-05", "last_certified": "2023-06-01", "certification_expiry": "2024-06-01", "description": "VPN user access to internal resources", "comments": "VPN team manages user access", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-003", "rule_name": "Guest Network Access", "rule_number": 3, "expiration_date": "2024-02-01 00:00:00", "expiration_type": "rule", "days_until_expiration": -528, "rule_owner": "", "rule_data": {"id": "R1004-003", "number": 3, "name": "Guest Network Access", "enabled": true, "source": "Guest_Network", "destination": "Internet", "service": "HTTP,HTTPS", "action": "Allow", "owner": "", "created_by": "temp.admin", "created_date": "2023-08-01", "last_modified": "2023-08-01", "expiration_date": "2024-02-01", "last_certified": "", "certification_expiry": "", "description": "Guest network internet access", "comments": "Temporary rule - no clear owner", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-001", "rule_name": "SMTP Inbound Mail", "rule_number": 1, "expiration_date": "2024-02-12 00:00:00", "expiration_type": "rule", "days_until_expiration": -517, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-001", "number": 1, "name": "SMTP Inbound Mail", "enabled": true, "source": "Internet", "destination": "Mail_Servers", "service": "SMTP,SMTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "mail.admin", "created_date": "2023-04-01", "last_modified": "2024-01-11", "expiration_date": "2024-02-12", "last_certified": "2023-04-01", "certification_expiry": "2024-04-01", "description": "Inbound email delivery", "comments": "Mail team - business critical", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-001", "rule_name": "SMTP Inbound Mail", "rule_number": 1, "expiration_date": "2024-04-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -468, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-001", "number": 1, "name": "SMTP Inbound Mail", "enabled": true, "source": "Internet", "destination": "Mail_Servers", "service": "SMTP,SMTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "mail.admin", "created_date": "2023-04-01", "last_modified": "2024-01-11", "expiration_date": "2024-02-12", "last_certified": "2023-04-01", "certification_expiry": "2024-04-01", "description": "Inbound email delivery", "comments": "Mail team - business critical", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-002", "rule_name": "IMAP/POP3 Access", "rule_number": 2, "expiration_date": "2024-01-20 00:00:00", "expiration_type": "rule", "days_until_expiration": -540, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-002", "number": 2, "name": "IMAP/POP3 Access", "enabled": true, "source": "Internal_Users", "destination": "Mail_Servers", "service": "IMAP,IMAPS,POP3,POP3S", "action": "Allow", "owner": "<EMAIL>", "created_by": "msg.admin", "created_date": "2023-05-15", "last_modified": "2023-12-20", "expiration_date": "2024-01-20", "last_certified": "2023-05-15", "certification_expiry": "2024-05-15", "description": "User email access via IMAP/POP3", "comments": "Messaging team responsibility", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-002", "rule_name": "IMAP/POP3 Access", "rule_number": 2, "expiration_date": "2024-05-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -424, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-002", "number": 2, "name": "IMAP/POP3 Access", "enabled": true, "source": "Internal_Users", "destination": "Mail_Servers", "service": "IMAP,IMAPS,POP3,POP3S", "action": "Allow", "owner": "<EMAIL>", "created_by": "msg.admin", "created_date": "2023-05-15", "last_modified": "2023-12-20", "expiration_date": "2024-01-20", "last_certified": "2023-05-15", "certification_expiry": "2024-05-15", "description": "User email access via IMAP/POP3", "comments": "Messaging team responsibility", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-003", "rule_name": "Mail Relay Legacy", "rule_number": 3, "expiration_date": "2024-01-22 00:00:00", "expiration_type": "rule", "days_until_expiration": -538, "rule_owner": "", "rule_data": {"id": "R1005-003", "number": 3, "name": "Mail Relay Legacy", "enabled": true, "source": "Legacy_Applications", "destination": "Mail_Relay", "service": "SMTP", "action": "Allow", "owner": "", "created_by": "unknown", "created_date": "2022-01-01", "last_modified": "2022-01-01", "expiration_date": "2024-01-22", "last_certified": "", "certification_expiry": "", "description": "Legacy application mail relay - created by unknown", "comments": "Old rule with no clear ownership", "risk_level": "high"}, "validated_owner": "<EMAIL>"}], "validation_results": {"total_rules": 27, "rules_with_valid_owners": [{"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-001", "rule_name": "DMZ Web Server Access", "rule_number": 1, "expiration_date": "2024-02-15 00:00:00", "expiration_type": "rule", "days_until_expiration": -514, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-001", "number": 1, "name": "DMZ Web Server Access", "enabled": true, "source": "Any", "destination": "DMZ_Web_Servers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "john.doe", "created_date": "2023-01-15", "last_modified": "2024-01-10", "expiration_date": "2024-02-15", "last_certified": "2023-02-15", "certification_expiry": "2024-02-15", "description": "Allow web traffic to DMZ web servers. Contact: <EMAIL> for changes.", "comments": "Annual recertification required", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-001", "rule_name": "DMZ Web Server Access", "rule_number": 1, "expiration_date": "2024-02-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -514, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-001", "number": 1, "name": "DMZ Web Server Access", "enabled": true, "source": "Any", "destination": "DMZ_Web_Servers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "john.doe", "created_date": "2023-01-15", "last_modified": "2024-01-10", "expiration_date": "2024-02-15", "last_certified": "2023-02-15", "certification_expiry": "2024-02-15", "description": "Allow web traffic to DMZ web servers. Contact: <EMAIL> for changes.", "comments": "Annual recertification required", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-002", "rule_name": "DMZ SSH Management", "rule_number": 2, "expiration_date": "2024-03-01 00:00:00", "expiration_type": "rule", "days_until_expiration": -499, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-002", "number": 2, "name": "DMZ SSH Management", "enabled": true, "source": "Management_Network", "destination": "DMZ_Servers", "service": "SSH", "action": "Allow", "owner": "<EMAIL>", "created_by": "admin", "created_date": "2023-03-01", "last_modified": "2024-01-05", "expiration_date": "2024-03-01", "last_certified": "2023-03-01", "certification_expiry": "2024-03-01", "description": "SSH access for DMZ server management", "comments": "Critical for operations - owner: <EMAIL>", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-002", "rule_name": "DMZ SSH Management", "rule_number": 2, "expiration_date": "2024-03-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -499, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1001-002", "number": 2, "name": "DMZ SSH Management", "enabled": true, "source": "Management_Network", "destination": "DMZ_Servers", "service": "SSH", "action": "Allow", "owner": "<EMAIL>", "created_by": "admin", "created_date": "2023-03-01", "last_modified": "2024-01-05", "expiration_date": "2024-03-01", "last_certified": "2023-03-01", "certification_expiry": "2024-03-01", "description": "SSH access for DMZ server management", "comments": "Critical for operations - owner: <EMAIL>", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1001, "device_name": "DMZ-FW-01", "rule_id": "R1001-003", "rule_name": "Legacy FTP Access", "rule_number": 3, "expiration_date": "2024-01-30 00:00:00", "expiration_type": "rule", "days_until_expiration": -530, "rule_owner": "", "rule_data": {"id": "R1001-003", "number": 3, "name": "Legacy FTP Access", "enabled": true, "source": "Internal_Network", "destination": "DMZ_FTP_Server", "service": "FTP", "action": "Allow", "owner": "", "created_by": "legacy.user", "created_date": "2022-06-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-30", "last_certified": "", "certification_expiry": "", "description": "Legacy FTP access - needs review", "comments": "No clear owner identified", "risk_level": "critical"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-001", "rule_name": "WEB Load Balancer Access", "rule_number": 1, "expiration_date": "2024-02-20 00:00:00", "expiration_type": "rule", "days_until_expiration": -509, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-001", "number": 1, "name": "WEB Load Balancer Access", "enabled": true, "source": "Internet", "destination": "Web_Load_Balancers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "web.admin", "created_date": "2023-05-01", "last_modified": "2024-01-12", "expiration_date": "2024-02-20", "last_certified": "2023-05-01", "certification_expiry": "2024-05-01", "description": "Public web access through load balancers", "comments": "Business critical rule", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-001", "rule_name": "WEB Load Balancer Access", "rule_number": 1, "expiration_date": "2024-05-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -438, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-001", "number": 1, "name": "WEB Load Balancer Access", "enabled": true, "source": "Internet", "destination": "Web_Load_Balancers", "service": "HTTP,HTTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "web.admin", "created_date": "2023-05-01", "last_modified": "2024-01-12", "expiration_date": "2024-02-20", "last_certified": "2023-05-01", "certification_expiry": "2024-05-01", "description": "Public web access through load balancers", "comments": "Business critical rule", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-002", "rule_name": "WEB Database Connection", "rule_number": 2, "expiration_date": "2024-02-10 00:00:00", "expiration_type": "rule", "days_until_expiration": -519, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-002", "number": 2, "name": "WEB Database Connection", "enabled": true, "source": "Web_Servers", "destination": "Database_Servers", "service": "MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "developer1", "created_date": "2023-04-15", "last_modified": "2024-01-08", "expiration_date": "2024-02-10", "last_certified": "2023-04-15", "certification_expiry": "2024-04-15", "description": "Web application database connectivity", "comments": "Application team responsible", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-002", "rule_name": "WEB Database Connection", "rule_number": 2, "expiration_date": "2024-04-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -454, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-002", "number": 2, "name": "WEB Database Connection", "enabled": true, "source": "Web_Servers", "destination": "Database_Servers", "service": "MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "developer1", "created_date": "2023-04-15", "last_modified": "2024-01-08", "expiration_date": "2024-02-10", "last_certified": "2023-04-15", "certification_expiry": "2024-04-15", "description": "Web application database connectivity", "comments": "Application team responsible", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-003", "rule_name": "WEB Monitoring Access", "rule_number": 3, "expiration_date": "2024-01-25 00:00:00", "expiration_type": "rule", "days_until_expiration": -535, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-003", "number": 3, "name": "WEB Monitoring Access", "enabled": true, "source": "Monitoring_Servers", "destination": "Web_Infrastructure", "service": "SNMP,HTTP", "action": "Allow", "owner": "<EMAIL>", "created_by": "monitor.admin", "created_date": "2023-07-01", "last_modified": "2023-12-15", "expiration_date": "2024-01-25", "last_certified": "2023-07-01", "certification_expiry": "2024-07-01", "description": "Infrastructure monitoring access", "comments": "Ops team maintains this rule", "risk_level": "low"}, "validated_owner": "<EMAIL>"}, {"device_id": 1002, "device_name": "WEB-FW-02", "rule_id": "R1002-003", "rule_name": "WEB Monitoring Access", "rule_number": 3, "expiration_date": "2024-07-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -377, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1002-003", "number": 3, "name": "WEB Monitoring Access", "enabled": true, "source": "Monitoring_Servers", "destination": "Web_Infrastructure", "service": "SNMP,HTTP", "action": "Allow", "owner": "<EMAIL>", "created_by": "monitor.admin", "created_date": "2023-07-01", "last_modified": "2023-12-15", "expiration_date": "2024-01-25", "last_certified": "2023-07-01", "certification_expiry": "2024-07-01", "description": "Infrastructure monitoring access", "comments": "Ops team maintains this rule", "risk_level": "low"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-001", "rule_name": "Database Replication", "rule_number": 1, "expiration_date": "2024-02-28 00:00:00", "expiration_type": "rule", "days_until_expiration": -501, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-001", "number": 1, "name": "Database Replication", "enabled": true, "source": "Primary_DB_Servers", "destination": "Secondary_DB_Servers", "service": "MySQL_Replication", "action": "Allow", "owner": "<EMAIL>", "created_by": "dba1", "created_date": "2023-02-01", "last_modified": "2024-01-03", "expiration_date": "2024-02-28", "last_certified": "2023-02-01", "certification_expiry": "2024-02-01", "description": "Database replication between primary and secondary servers", "comments": "Critical for DR - DBA team owns this", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-001", "rule_name": "Database Replication", "rule_number": 1, "expiration_date": "2024-02-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -528, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-001", "number": 1, "name": "Database Replication", "enabled": true, "source": "Primary_DB_Servers", "destination": "Secondary_DB_Servers", "service": "MySQL_Replication", "action": "Allow", "owner": "<EMAIL>", "created_by": "dba1", "created_date": "2023-02-01", "last_modified": "2024-01-03", "expiration_date": "2024-02-28", "last_certified": "2023-02-01", "certification_expiry": "2024-02-01", "description": "Database replication between primary and secondary servers", "comments": "Critical for DR - DBA team owns this", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-002", "rule_name": "Database Backup Access", "rule_number": 2, "expiration_date": "2024-01-31 00:00:00", "expiration_type": "rule", "days_until_expiration": -529, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-002", "number": 2, "name": "Database Backup Access", "enabled": true, "source": "Backup_Servers", "destination": "Database_Servers", "service": "Custom_Backup_Port", "action": "Allow", "owner": "<EMAIL>", "created_by": "backup.admin", "created_date": "2023-01-10", "last_modified": "2023-11-20", "expiration_date": "2024-01-31", "last_certified": "2023-01-10", "certification_expiry": "2024-01-10", "description": "Backup system access to databases", "comments": "Backup team responsible", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-002", "rule_name": "Database Backup Access", "rule_number": 2, "expiration_date": "2024-01-10 00:00:00", "expiration_type": "certification", "days_until_expiration": -550, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-002", "number": 2, "name": "Database Backup Access", "enabled": true, "source": "Backup_Servers", "destination": "Database_Servers", "service": "Custom_Backup_Port", "action": "Allow", "owner": "<EMAIL>", "created_by": "backup.admin", "created_date": "2023-01-10", "last_modified": "2023-11-20", "expiration_date": "2024-01-31", "last_certified": "2023-01-10", "certification_expiry": "2024-01-10", "description": "Backup system access to databases", "comments": "Backup team responsible", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-003", "rule_name": "Database Admin Access", "rule_number": 3, "expiration_date": "2024-03-15 00:00:00", "expiration_type": "rule", "days_until_expiration": -485, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-003", "number": 3, "name": "Database Admin Access", "enabled": true, "source": "DBA_Workstations", "destination": "All_Database_Servers", "service": "SSH,RDP,MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "senior.dba", "created_date": "2023-03-15", "last_modified": "2024-01-01", "expiration_date": "2024-03-15", "last_certified": "2023-03-15", "certification_expiry": "2024-03-15", "description": "DBA administrative access to all database servers", "comments": "Senior DBA approval required for changes", "risk_level": "critical"}, "validated_owner": "<EMAIL>"}, {"device_id": 1003, "device_name": "DB-FW-03", "rule_id": "R1003-003", "rule_name": "Database Admin Access", "rule_number": 3, "expiration_date": "2024-03-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -485, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1003-003", "number": 3, "name": "Database Admin Access", "enabled": true, "source": "DBA_Workstations", "destination": "All_Database_Servers", "service": "SSH,RDP,MySQL,PostgreSQL", "action": "Allow", "owner": "<EMAIL>", "created_by": "senior.dba", "created_date": "2023-03-15", "last_modified": "2024-01-01", "expiration_date": "2024-03-15", "last_certified": "2023-03-15", "certification_expiry": "2024-03-15", "description": "DBA administrative access to all database servers", "comments": "Senior DBA approval required for changes", "risk_level": "critical"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-001", "rule_name": "Core Network Routing", "rule_number": 1, "expiration_date": "2024-01-28 00:00:00", "expiration_type": "rule", "days_until_expiration": -532, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-001", "number": 1, "name": "Core Network Routing", "enabled": true, "source": "Internal_Networks", "destination": "Core_Infrastructure", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "network.admin", "created_date": "2023-01-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-28", "last_certified": "2023-01-01", "certification_expiry": "2024-01-01", "description": "Core network routing and connectivity", "comments": "Network team maintains - critical infrastructure", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-001", "rule_name": "Core Network Routing", "rule_number": 1, "expiration_date": "2024-01-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -559, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-001", "number": 1, "name": "Core Network Routing", "enabled": true, "source": "Internal_Networks", "destination": "Core_Infrastructure", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "network.admin", "created_date": "2023-01-01", "last_modified": "2023-12-01", "expiration_date": "2024-01-28", "last_certified": "2023-01-01", "certification_expiry": "2024-01-01", "description": "Core network routing and connectivity", "comments": "Network team maintains - critical infrastructure", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-002", "rule_name": "VPN Access Rule", "rule_number": 2, "expiration_date": "2024-02-05 00:00:00", "expiration_type": "rule", "days_until_expiration": -524, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-002", "number": 2, "name": "VPN Access Rule", "enabled": true, "source": "VPN_Clients", "destination": "Internal_Resources", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "vpn.admin", "created_date": "2023-06-01", "last_modified": "2024-01-07", "expiration_date": "2024-02-05", "last_certified": "2023-06-01", "certification_expiry": "2024-06-01", "description": "VPN user access to internal resources", "comments": "VPN team manages user access", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-002", "rule_name": "VPN Access Rule", "rule_number": 2, "expiration_date": "2024-06-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -407, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1004-002", "number": 2, "name": "VPN Access Rule", "enabled": true, "source": "VPN_Clients", "destination": "Internal_Resources", "service": "Any", "action": "Allow", "owner": "<EMAIL>", "created_by": "vpn.admin", "created_date": "2023-06-01", "last_modified": "2024-01-07", "expiration_date": "2024-02-05", "last_certified": "2023-06-01", "certification_expiry": "2024-06-01", "description": "VPN user access to internal resources", "comments": "VPN team manages user access", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1004, "device_name": "CORE-FW-04", "rule_id": "R1004-003", "rule_name": "Guest Network Access", "rule_number": 3, "expiration_date": "2024-02-01 00:00:00", "expiration_type": "rule", "days_until_expiration": -528, "rule_owner": "", "rule_data": {"id": "R1004-003", "number": 3, "name": "Guest Network Access", "enabled": true, "source": "Guest_Network", "destination": "Internet", "service": "HTTP,HTTPS", "action": "Allow", "owner": "", "created_by": "temp.admin", "created_date": "2023-08-01", "last_modified": "2023-08-01", "expiration_date": "2024-02-01", "last_certified": "", "certification_expiry": "", "description": "Guest network internet access", "comments": "Temporary rule - no clear owner", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-001", "rule_name": "SMTP Inbound Mail", "rule_number": 1, "expiration_date": "2024-02-12 00:00:00", "expiration_type": "rule", "days_until_expiration": -517, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-001", "number": 1, "name": "SMTP Inbound Mail", "enabled": true, "source": "Internet", "destination": "Mail_Servers", "service": "SMTP,SMTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "mail.admin", "created_date": "2023-04-01", "last_modified": "2024-01-11", "expiration_date": "2024-02-12", "last_certified": "2023-04-01", "certification_expiry": "2024-04-01", "description": "Inbound email delivery", "comments": "Mail team - business critical", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-001", "rule_name": "SMTP Inbound Mail", "rule_number": 1, "expiration_date": "2024-04-01 00:00:00", "expiration_type": "certification", "days_until_expiration": -468, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-001", "number": 1, "name": "SMTP Inbound Mail", "enabled": true, "source": "Internet", "destination": "Mail_Servers", "service": "SMTP,SMTPS", "action": "Allow", "owner": "<EMAIL>", "created_by": "mail.admin", "created_date": "2023-04-01", "last_modified": "2024-01-11", "expiration_date": "2024-02-12", "last_certified": "2023-04-01", "certification_expiry": "2024-04-01", "description": "Inbound email delivery", "comments": "Mail team - business critical", "risk_level": "high"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-002", "rule_name": "IMAP/POP3 Access", "rule_number": 2, "expiration_date": "2024-01-20 00:00:00", "expiration_type": "rule", "days_until_expiration": -540, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-002", "number": 2, "name": "IMAP/POP3 Access", "enabled": true, "source": "Internal_Users", "destination": "Mail_Servers", "service": "IMAP,IMAPS,POP3,POP3S", "action": "Allow", "owner": "<EMAIL>", "created_by": "msg.admin", "created_date": "2023-05-15", "last_modified": "2023-12-20", "expiration_date": "2024-01-20", "last_certified": "2023-05-15", "certification_expiry": "2024-05-15", "description": "User email access via IMAP/POP3", "comments": "Messaging team responsibility", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-002", "rule_name": "IMAP/POP3 Access", "rule_number": 2, "expiration_date": "2024-05-15 00:00:00", "expiration_type": "certification", "days_until_expiration": -424, "rule_owner": "<EMAIL>", "rule_data": {"id": "R1005-002", "number": 2, "name": "IMAP/POP3 Access", "enabled": true, "source": "Internal_Users", "destination": "Mail_Servers", "service": "IMAP,IMAPS,POP3,POP3S", "action": "Allow", "owner": "<EMAIL>", "created_by": "msg.admin", "created_date": "2023-05-15", "last_modified": "2023-12-20", "expiration_date": "2024-01-20", "last_certified": "2023-05-15", "certification_expiry": "2024-05-15", "description": "User email access via IMAP/POP3", "comments": "Messaging team responsibility", "risk_level": "medium"}, "validated_owner": "<EMAIL>"}, {"device_id": 1005, "device_name": "MAIL-FW-05", "rule_id": "R1005-003", "rule_name": "Mail Relay Legacy", "rule_number": 3, "expiration_date": "2024-01-22 00:00:00", "expiration_type": "rule", "days_until_expiration": -538, "rule_owner": "", "rule_data": {"id": "R1005-003", "number": 3, "name": "Mail Relay Legacy", "enabled": true, "source": "Legacy_Applications", "destination": "Mail_Relay", "service": "SMTP", "action": "Allow", "owner": "", "created_by": "unknown", "created_date": "2022-01-01", "last_modified": "2022-01-01", "expiration_date": "2024-01-22", "last_certified": "", "certification_expiry": "", "description": "Legacy application mail relay - created by unknown", "comments": "Old rule with no clear ownership", "risk_level": "high"}, "validated_owner": "<EMAIL>"}], "rules_without_owners": [], "validation_summary": {"rules_with_owners": 27, "rules_without_owners": 0, "unique_owners": 9, "owner_distribution": {"<EMAIL>": 3}}}, "summary": {"total_rules": 27, "rules_with_owners": 27, "rules_without_owners": 0, "owner_detection_rate": 100.0}}