#!/usr/bin/env python3
"""
Test Rule Ownership Detection with Dummy Data

This script demonstrates how the rule ownership detection works
using realistic dummy data that simulates Tufin API responses.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any

# Import our modules
from mock_tufin_api_client import MockTufinAPIClient
from rule_owner_manager import RuleOwnerManager
from securechange_ticket_manager import SecureChangeTicketManager

def setup_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_mock_api_client():
    """Test the mock API client with dummy data"""
    print("=== Testing Mock API Client ===")
    
    # Initialize mock client
    client = MockTufinAPIClient()
    
    # Test connection
    connection_result = client.test_connection()
    print(f"✅ Connection test: {connection_result['message']}")
    
    # Get devices
    devices = client.get_devices()
    print(f"📱 Found {len(devices)} devices:")
    for device in devices:
        print(f"   - {device['name']} (ID: {device['id']}) - Owner: {device.get('owner', 'Unknown')}")
    
    # Get rules for first device
    if devices:
        device_id = devices[0]['id']
        device_name = devices[0]['name']
        rules = client.get_device_rules(device_id)
        print(f"\n📋 Found {len(rules)} rules for {device_name}:")
        for rule in rules:
            print(f"   - {rule['name']} (ID: {rule['id']}) - Owner: {rule.get('owner', 'None')}")
    
    # Check for expiring rules
    print(f"\n⏰ Checking for expiring rules...")
    expiring_rules = client.get_rules_with_expiration(days_ahead=30)
    print(f"Found {len(expiring_rules)} expiring rules:")
    
    for rule in expiring_rules:
        print(f"   - {rule['rule_name']} on {rule['device_name']}")
        print(f"     Expires: {rule['expiration_date'].strftime('%Y-%m-%d')} ({rule['days_until_expiration']} days)")
        print(f"     Type: {rule['expiration_type']}")
        print(f"     Owner: {rule['rule_owner'] or 'None'}")
        print()
    
    return expiring_rules

def test_owner_detection(expiring_rules):
    """Test rule owner detection logic"""
    print("=== Testing Owner Detection ===")
    
    # Initialize mock client and owner manager
    client = MockTufinAPIClient()
    
    # Custom owner mapping for testing
    owner_mapping = {
        "DMZ": "<EMAIL>",
        "WEB": "<EMAIL>", 
        "DATABASE": "<EMAIL>",
        "DB": "<EMAIL>",
        "MAIL": "<EMAIL>",
        "CORE": "<EMAIL>"
    }
    
    owner_manager = RuleOwnerManager(
        client,
        default_owner="<EMAIL>",
        owner_mapping=owner_mapping
    )
    
    print("🔍 Analyzing rule ownership...")
    
    for rule in expiring_rules:
        print(f"\nRule: {rule['rule_name']} (ID: {rule['rule_id']})")
        print(f"Device: {rule['device_name']}")
        print(f"Explicit Owner: {rule['rule_owner'] or 'None'}")
        
        # Test different owner detection methods
        detected_owner = owner_manager.get_rule_owner(rule)
        print(f"Detected Owner: {detected_owner or 'None'}")
        
        # Test owner validation
        if detected_owner:
            is_valid = owner_manager.validate_user(detected_owner)
            print(f"Owner Valid: {'✅ Yes' if is_valid else '❌ No'}")
        
        # Get final validated owner
        validated_owner = owner_manager.get_validated_owner(rule)
        print(f"Final Owner: {validated_owner or 'None'}")
        
        print("-" * 50)

def test_bulk_owner_validation(expiring_rules):
    """Test bulk owner validation"""
    print("\n=== Testing Bulk Owner Validation ===")
    
    client = MockTufinAPIClient()
    
    owner_mapping = {
        "DMZ": "<EMAIL>",
        "WEB": "<EMAIL>",
        "DATABASE": "<EMAIL>", 
        "DB": "<EMAIL>",
        "MAIL": "<EMAIL>",
        "CORE": "<EMAIL>"
    }
    
    owner_manager = RuleOwnerManager(
        client,
        default_owner="<EMAIL>",
        owner_mapping=owner_mapping
    )
    
    # Perform bulk validation
    validation_results = owner_manager.bulk_validate_owners(expiring_rules)
    
    print("📊 Bulk Validation Results:")
    summary = validation_results['validation_summary']
    print(f"   Total rules: {summary['rules_with_owners'] + summary['rules_without_owners']}")
    print(f"   Rules with valid owners: {summary['rules_with_owners']}")
    print(f"   Rules without owners: {summary['rules_without_owners']}")
    print(f"   Unique owners: {summary['unique_owners']}")
    
    print("\n👥 Owner Distribution:")
    for owner, count in summary['owner_distribution'].items():
        print(f"   {owner}: {count} rules")
    
    print("\n📋 Rules with Valid Owners:")
    for rule in validation_results['rules_with_valid_owners']:
        print(f"   - {rule['rule_name']} → {rule['validated_owner']}")
    
    if validation_results['rules_without_owners']:
        print("\n❓ Rules without Valid Owners:")
        for rule in validation_results['rules_without_owners']:
            print(f"   - {rule['rule_name']} (Device: {rule['device_name']})")
    
    return validation_results

def test_mock_ticket_creation(rules_with_owners):
    """Test mock ticket creation (simulation only)"""
    print("\n=== Testing Mock Ticket Creation ===")
    
    client = MockTufinAPIClient()
    
    # Create a mock ticket manager that doesn't actually create tickets
    class MockSecureChangeTicketManager:
        def __init__(self, api_client):
            self.api_client = api_client
            self.logger = logging.getLogger(__name__)
            self.ticket_counter = 1000
        
        def create_recertification_ticket(self, rule_info, assignee=None, custom_template=None):
            """Mock ticket creation"""
            self.ticket_counter += 1
            ticket_id = f"MOCK-{self.ticket_counter}"
            
            self.logger.info(f"Mock ticket created: {ticket_id} for rule {rule_info.get('rule_id')}")
            
            return {
                'success': True,
                'ticket_id': ticket_id,
                'ticket_url': f"https://mock-tufin.local/tickets/{ticket_id}",
                'ticket_data': {
                    'id': ticket_id,
                    'subject': f"Rule Recertification: {rule_info.get('rule_name')}",
                    'assignee': assignee,
                    'status': 'open'
                },
                'rule_info': rule_info
            }
        
        def bulk_create_recertification_tickets(self, rules_list, auto_assign=True):
            """Mock bulk ticket creation"""
            results = {
                'total_rules': len(rules_list),
                'successful_tickets': [],
                'failed_tickets': [],
                'assignment_results': []
            }
            
            for rule_info in rules_list:
                ticket_result = self.create_recertification_ticket(rule_info)
                results['successful_tickets'].append(ticket_result)
                
                if auto_assign and rule_info.get('validated_owner'):
                    assignment_result = {
                        'success': True,
                        'ticket_id': ticket_result['ticket_id'],
                        'assignee': rule_info['validated_owner']
                    }
                    results['assignment_results'].append(assignment_result)
            
            return results
    
    ticket_manager = MockSecureChangeTicketManager(client)
    
    # Create tickets for rules with valid owners
    print(f"🎫 Creating mock tickets for {len(rules_with_owners)} rules...")
    
    ticket_results = ticket_manager.bulk_create_recertification_tickets(
        rules_with_owners,
        auto_assign=True
    )
    
    print(f"✅ Successfully created {len(ticket_results['successful_tickets'])} mock tickets")
    print(f"📌 Successfully assigned {len(ticket_results['assignment_results'])} tickets")
    
    print("\n🎫 Created Tickets:")
    for ticket in ticket_results['successful_tickets']:
        rule_info = ticket['rule_info']
        assignee = rule_info.get('validated_owner', 'Unassigned')
        print(f"   - {ticket['ticket_id']}: {rule_info['rule_name']} → {assignee}")
    
    return ticket_results

def generate_test_report(expiring_rules, validation_results, ticket_results):
    """Generate a test report"""
    print("\n" + "="*60)
    print("RULE OWNERSHIP DETECTION TEST REPORT")
    print("="*60)
    
    print(f"📊 Summary:")
    print(f"   Total expiring rules found: {len(expiring_rules)}")
    print(f"   Rules with valid owners: {len(validation_results['rules_with_valid_owners'])}")
    print(f"   Rules without owners: {len(validation_results['rules_without_owners'])}")
    print(f"   Mock tickets created: {len(ticket_results['successful_tickets'])}")
    
    print(f"\n🎯 Owner Detection Success Rate:")
    total_rules = len(expiring_rules)
    rules_with_owners = len(validation_results['rules_with_valid_owners'])
    success_rate = (rules_with_owners / total_rules * 100) if total_rules > 0 else 0
    print(f"   {success_rate:.1f}% ({rules_with_owners}/{total_rules})")
    
    print(f"\n📋 Detailed Results:")
    
    # Group by owner detection method
    explicit_owners = []
    mapped_owners = []
    default_owners = []
    no_owners = []
    
    for rule in expiring_rules:
        rule_owner = rule.get('rule_owner', '').strip()
        validated_owner = None
        
        # Find validated owner
        for validated_rule in validation_results['rules_with_valid_owners']:
            if validated_rule['rule_id'] == rule['rule_id']:
                validated_owner = validated_rule['validated_owner']
                break
        
        if validated_owner:
            if rule_owner and rule_owner == validated_owner:
                explicit_owners.append(rule)
            elif validated_owner == "<EMAIL>":
                default_owners.append(rule)
            else:
                mapped_owners.append(rule)
        else:
            no_owners.append(rule)
    
    print(f"   Explicit owners: {len(explicit_owners)} rules")
    print(f"   Mapped owners: {len(mapped_owners)} rules")
    print(f"   Default owner assigned: {len(default_owners)} rules")
    print(f"   No valid owner found: {len(no_owners)} rules")
    
    if no_owners:
        print(f"\n❓ Rules without valid owners:")
        for rule in no_owners:
            print(f"   - {rule['rule_name']} on {rule['device_name']}")
    
    print("\n" + "="*60)

def main():
    """Run the complete test suite"""
    print("Tufin Rule Ownership Detection Test")
    print("Using Dummy Data for Local Testing")
    print("="*50)
    
    setup_logging()
    
    try:
        # Test 1: Mock API client
        expiring_rules = test_mock_api_client()
        
        if not expiring_rules:
            print("❌ No expiring rules found in dummy data")
            return
        
        # Test 2: Owner detection
        test_owner_detection(expiring_rules)
        
        # Test 3: Bulk validation
        validation_results = test_bulk_owner_validation(expiring_rules)
        
        # Test 4: Mock ticket creation
        rules_with_owners = validation_results['rules_with_valid_owners']
        ticket_results = test_mock_ticket_creation(rules_with_owners)
        
        # Test 5: Generate report
        generate_test_report(expiring_rules, validation_results, ticket_results)
        
        print("\n✅ All tests completed successfully!")
        print("\nNext steps:")
        print("1. Review the owner detection logic and mappings")
        print("2. Adjust owner_mapping in your configuration")
        print("3. Test with real Tufin API when ready")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
