#!/usr/bin/env python3
"""
Test script for simplified CSV validation - auto-detection only, no field formatting.
"""

from csv_processor_simple import csv_to_dict_simple, validate_csv_structure
from security_validator import SecurityValidator

def create_test_csv():
    """Create a test CSV with various header amounts."""
    
    # Test with moderate header content
    test_data = [
        "# Network Security Analysis Report",
        "# Generated: 2024-01-15",
        "# Classification: Internal Use",
        "#",
        "# This report contains network flow analysis",
        "# for security assessment purposes.",
        "#",
        "# Instructions:",
        "# - Review all critical findings immediately",
        "# - Escalate high-risk items within 4 hours",
        "# - Document all remediation actions",
        "#",
        "# Analysis Parameters:",
        "# - Time Period: 24 hours",
        "# - Network Segments: All internal",
        "# - Detection Accuracy: 99.7%",
        "#",
        ",Source IP,Destination IP,Port,Service,Action,File Type",
        "Flow,************,************0,22,ssh,Allow,key",
        "Flow,*********,**********,21,ftp,Allow,exe",
        "Flow,***********,***********0,443,https,Allow,pdf",
        "Flow,************,************0,23,telnet,Deny,bat,vbs",
        "Flow,***********,***********0,80,http,Allow,zip"
    ]
    
    with open("test_simplified.csv", "w", encoding="utf-8") as f:
        for line in test_data:
            f.write(line + "\n")
    
    print(f"✅ Created test_simplified.csv with {len(test_data)} total rows")
    print(f"   📊 Header/instruction rows: {len(test_data) - 6}")
    print(f"   📋 Data rows: 5")

def test_simplified_processing():
    """Test the simplified CSV processing."""
    
    print("🧪 Testing Simplified CSV Processing")
    print("=" * 50)
    
    create_test_csv()
    
    print(f"\n🔍 Testing auto-detection (no field formatting)...")
    
    # Test auto-detection
    flow_data = csv_to_dict_simple("test_simplified.csv")
    
    if flow_data:
        print(f"\n📋 Raw Data Sample (no formatting applied):")
        sample_flow = next(iter(flow_data.values()))
        for field, value in sample_flow.items():
            print(f"   {field}: '{value}'")
        
        # Test basic validation
        print(f"\n🔍 Running basic structure validation...")
        validated_data, validation_issues = validate_csv_structure(flow_data)
        
        print(f"\n📊 Validation Results:")
        for issue in validation_issues:
            print(f"   {issue}")
        
        # Run security validation on raw data
        print(f"\n🔍 Running security validation on raw data...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        print(f"\n📊 Security Results:")
        print(f"   • Total flows: {results['total_flows']}")
        print(f"   • Critical issues: {results['critical_issues']} 🔴")
        print(f"   • High risk issues: {results['high_risk_issues']} 🟠")
        print(f"   • Medium risk issues: {results['medium_risk_issues']} 🟡")
        
        # Show some detections
        print(f"\n📁 Sample Security Detections:")
        for flow_id, flow_result in list(results["flow_results"].items())[:3]:
            flow_data_item = flow_result.get("flow_data", {})
            file_type = flow_data_item.get("File Type", "N/A")
            service = flow_data_item.get("Service", "N/A")
            action = flow_data_item.get("Action", "N/A")
            
            # Check for issues
            issues = flow_result.get("issues", [])
            if issues:
                highest_risk = max(issue.get("risk_level", "low") for issue in issues)
                risk_emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}.get(highest_risk, "⚪")
                print(f"   {risk_emoji} {flow_id}: {file_type} via {service} ({action}) - {len(issues)} issues")
            else:
                print(f"   ✅ {flow_id}: {file_type} via {service} ({action}) - No issues")
        
        print(f"\n✅ Simplified processing completed successfully!")
        
    else:
        print(f"❌ Failed to load data")

def test_edge_cases():
    """Test edge cases with simplified processing."""
    
    print(f"\n🔧 Testing Edge Cases")
    print("=" * 30)
    
    # Test with minimal CSV
    minimal_csv = [
        ",Col1,Col2,Col3",
        "Flow,Value1,Value2,Value3"
    ]
    
    with open("minimal_test.csv", "w", encoding="utf-8") as f:
        for line in minimal_csv:
            f.write(line + "\n")
    
    print(f"📁 Testing minimal CSV...")
    flow_data = csv_to_dict_simple("minimal_test.csv")
    
    if flow_data:
        print(f"   ✅ Loaded {len(flow_data)} flows from minimal CSV")
        sample = next(iter(flow_data.values()))
        print(f"   📋 Columns: {list(sample.keys())}")
    else:
        print(f"   ❌ Failed to load minimal CSV")
    
    # Test with no Flow rows
    no_flow_csv = [
        "Header1,Header2,Header3",
        "Data1,Data2,Data3",
        "More1,More2,More3"
    ]
    
    with open("no_flow_test.csv", "w", encoding="utf-8") as f:
        for line in no_flow_csv:
            f.write(line + "\n")
    
    print(f"\n📁 Testing CSV with no Flow rows...")
    flow_data = csv_to_dict_simple("no_flow_test.csv")
    
    if flow_data:
        print(f"   ⚠️  Unexpectedly loaded {len(flow_data)} flows")
    else:
        print(f"   ✅ Correctly detected no Flow rows")

def main():
    """Main test function."""
    
    print("🚀 Simplified CSV Validation Test Suite")
    print("=" * 60)
    print("Testing auto-detection only, no field formatting")
    
    # Test simplified processing
    test_simplified_processing()
    
    # Test edge cases
    test_edge_cases()
    
    print(f"\n✅ All tests completed!")
    print(f"\n🎯 Key Features Demonstrated:")
    print(f"   🤖 Automatic table detection only")
    print(f"   📊 No field formatting or data cleaning")
    print(f"   🔍 Basic structure validation")
    print(f"   📋 Raw data passed to security validation")
    print(f"   ⚡ Simplified, focused functionality")
    
    print(f"\n💡 Benefits of Simplified Approach:")
    print(f"   • Faster processing (no complex validation)")
    print(f"   • Preserves original data format")
    print(f"   • Focuses on core functionality: find table, load data")
    print(f"   • Automatic detection or fail (no manual fallback)")
    print(f"   • Cleaner, more maintainable code")

if __name__ == "__main__":
    main()
