#!/usr/bin/env python3
"""
Test script for CSV row skipping functionality.
Demonstrates how to skip the first 31 rows when importing CSV files.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator

def create_test_csv_with_header_rows():
    """Create a test CSV file with 31 header/metadata rows before the actual data."""
    
    csv_content = []
    
    # Add 31 rows of metadata/headers
    csv_content.append("# Network Flow Analysis Report")
    csv_content.append("# Generated: 2024-01-15")
    csv_content.append("# Source: Security Team")
    csv_content.append("#")
    csv_content.append("# Configuration:")
    csv_content.append("# - Analysis Period: Last 30 days")
    csv_content.append("# - Filter: High-risk protocols")
    csv_content.append("# - Scope: All network segments")
    csv_content.append("#")
    csv_content.append("# Summary Statistics:")
    csv_content.append("# - Total Flows: 1,234")
    csv_content.append("# - Unique Sources: 45")
    csv_content.append("# - Unique Destinations: 67")
    csv_content.append("# - Risk Categories: 4")
    csv_content.append("#")
    csv_content.append("# Risk Distribution:")
    csv_content.append("# - Critical: 12 flows")
    csv_content.append("# - High: 34 flows")
    csv_content.append("# - Medium: 56 flows")
    csv_content.append("# - Low: 78 flows")
    csv_content.append("#")
    csv_content.append("# File Type Analysis:")
    csv_content.append("# - Executable files: 15")
    csv_content.append("# - Archive files: 23")
    csv_content.append("# - Script files: 8")
    csv_content.append("# - Document files: 45")
    csv_content.append("#")
    csv_content.append("# Notes:")
    csv_content.append("# - All timestamps in UTC")
    csv_content.append("# - IP addresses anonymized")
    csv_content.append("# - File types detected via deep inspection")
    csv_content.append("#")
    
    # Row 32: The actual header row
    csv_content.append(",Source IP,Destination IP,Port,Service,Action,File Type")
    
    # Rows 33+: The actual data
    csv_content.append("Flow,***********,***********0,111,https,Allow,pdf")
    csv_content.append("Flow,***********,***********0,222,https,Allow,exe")
    csv_content.append("Flow,***********,***********0,333,ssh,Allow,zip")
    csv_content.append("Flow,***********,***********0,444,ftp,Allow,\"exe,dll\"")
    csv_content.append("Flow,***********,***********0,555,telnet,Allow,\"bat,vbs,ps1\"")
    
    # Write to file
    with open("Example_with_31_header_rows.csv", "w", encoding="utf-8") as f:
        for line in csv_content:
            f.write(line + "\n")
    
    print(f"✅ Created test CSV with {len(csv_content)} total rows")
    print(f"   • Rows 1-31: Metadata/comments")
    print(f"   • Row 32: Column headers")
    print(f"   • Rows 33+: Actual data")

def test_csv_import_with_skip_rows():
    """Test CSV import with different skip_rows values."""
    
    print("🔍 Testing CSV Import with Row Skipping")
    print("=" * 50)
    
    csv_file = "Example_with_31_header_rows.csv"
    
    # Test 1: Import without skipping rows (should fail or give wrong results)
    print(f"\n📋 Test 1: Import without skipping rows")
    try:
        flow_data_no_skip = csv_to_dict_simple(csv_file, skip_rows=0)
        print(f"  Result: {len(flow_data_no_skip)} flows loaded")
        if flow_data_no_skip:
            first_flow = next(iter(flow_data_no_skip.values()))
            print(f"  First flow headers: {list(first_flow.keys())}")
            print(f"  First flow data: {first_flow}")
        else:
            print(f"  No flows loaded (expected - headers are comments)")
    except Exception as e:
        print(f"  Error: {e}")
    
    # Test 2: Import with skipping 32 rows (correct approach)
    print(f"\n📋 Test 2: Import with skipping 32 rows")
    try:
        flow_data_skip_32 = csv_to_dict_simple(csv_file, skip_rows=32)
        print(f"  Result: {len(flow_data_skip_32)} flows loaded")
        if flow_data_skip_32:
            first_flow = next(iter(flow_data_skip_32.values()))
            print(f"  Headers: {list(first_flow.keys())}")
            print(f"  Sample flow data:")
            for flow_id, flow_data in list(flow_data_skip_32.items())[:2]:
                print(f"    {flow_id}: {flow_data}")

        return flow_data_skip_32
        
    except Exception as e:
        print(f"  Error: {e}")
        return None
    
    # Test 3: Import with skipping too many rows
    print(f"\n📋 Test 3: Import with skipping too many rows (should fail)")
    try:
        flow_data_skip_too_many = csv_to_dict_simple(csv_file, skip_rows=50)
        print(f"  Result: {len(flow_data_skip_too_many)} flows loaded")
    except Exception as e:
        print(f"  Error: {e}")

def test_security_validation_with_skipped_rows():
    """Test security validation on CSV data loaded with row skipping."""
    
    print(f"\n🔍 Testing Security Validation with Skipped Rows")
    print("=" * 55)
    
    csv_file = "Example_with_31_header_rows.csv"
    
    try:
        # Load CSV with proper row skipping
        print(f"📁 Loading CSV with skip_rows=32...")
        flow_data = csv_to_dict_simple(csv_file, skip_rows=32)
        
        if not flow_data:
            print(f"❌ No data loaded")
            return None
        
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Run security validation
        print(f"\n🔍 Running security validation...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        print(f"✅ Validation completed")
        print(f"📊 Results summary:")
        print(f"  • Total flows: {results['total_flows']}")
        print(f"  • Flows with issues: {results['flows_with_issues']}")
        print(f"  • Critical issues: {results['critical_issues']} 🔴")
        print(f"  • High risk issues: {results['high_risk_issues']} 🟠")
        print(f"  • Medium risk issues: {results['medium_risk_issues']} 🟡")
        
        # Show file type detections
        print(f"\n📁 File Type Detections:")
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type = flow_data_item.get("File Type", "N/A")
            service = flow_data_item.get("Service", "N/A")
            
            # Check for file type issues
            file_type_issues = [issue for issue in flow_result.get("issues", []) 
                              if issue.get("field") == "File Type"]
            
            if file_type_issues:
                for issue in file_type_issues:
                    risk_level = issue.get("risk_level", "unknown")
                    value = issue.get("value", "unknown")
                    
                    risk_emoji = {
                        "critical": "🔴",
                        "high": "🟠", 
                        "medium": "🟡",
                        "low": "🟢"
                    }.get(risk_level, "⚪")
                    
                    print(f"  {risk_emoji} {flow_id}: {value} file via {service}")
            else:
                print(f"  ✅ {flow_id}: {file_type} file via {service} (no issues)")
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function to demonstrate row skipping functionality."""
    
    print("🚀 CSV Row Skipping Test Suite")
    print("=" * 60)
    
    # Create test CSV with header rows
    create_test_csv_with_header_rows()
    
    # Test CSV import with different skip values
    flow_data = test_csv_import_with_skip_rows()
    
    # Test security validation
    if flow_data:
        results = test_security_validation_with_skipped_rows()
        
        if results:
            print(f"\n✅ All tests completed successfully!")
            print(f"\n💡 Usage Example:")
            print(f"  # Skip first 31 rows when importing CSV")
            print(f"  flow_data = csv_to_dict_simple('your_file.csv', skip_rows=31)")
            print(f"  ")
            print(f"  # Then run validation as normal")
            print(f"  validator = SecurityValidator('unacceptable_values.json', 'security_guidance.json')")
            print(f"  results = validator.validate_flow_data(flow_data)")
    else:
        print(f"\n❌ Tests failed!")

if __name__ == "__main__":
    main()
