#!/usr/bin/env python3
"""
Test script for YAML export functionality.
"""

from security_validator import csv_to_dict_simple, export_original_flows_to_yaml

def test_yaml_export():
    """Test the YAML export functionality."""
    
    print("📤 Testing YAML Export")
    print("=" * 40)
    
    csv_file = "test_comma_expansion.csv"  # Change to your CSV file
    
    try:
        # Step 1: Load CSV data (with expansion)
        print(f"\n1️⃣ Loading CSV data...")
        flow_data = csv_to_dict_simple(csv_file, expand_comma_separated=True)
        print(f"   Loaded {len(flow_data)} total flows")
        
        # Show what flows we have
        original_flows = []
        expanded_flows = []
        
        for flow_id in flow_data.keys():
            if '_' in flow_id and flow_id.split('_')[-1].isalpha() and len(flow_id.split('_')[-1]) == 1:
                expanded_flows.append(flow_id)
            else:
                original_flows.append(flow_id)
        
        print(f"   Original flows: {len(original_flows)} - {original_flows}")
        print(f"   Expanded flows: {len(expanded_flows)} - {expanded_flows[:5]}{'...' if len(expanded_flows) > 5 else ''}")
        
        # Step 2: Export original flows only
        print(f"\n2️⃣ Exporting original flows to YAML...")
        yaml_file = export_original_flows_to_yaml(
            flow_data, 
            "original_flows_only.yaml", 
            include_expanded=False
        )
        print(f"   ✅ Original flows exported to: {yaml_file}")
        
        # Step 3: Export all flows (including expanded)
        print(f"\n3️⃣ Exporting all flows to YAML...")
        yaml_file_all = export_original_flows_to_yaml(
            flow_data, 
            "all_flows_including_expanded.yaml", 
            include_expanded=True
        )
        print(f"   ✅ All flows exported to: {yaml_file_all}")
        
        # Step 4: Show sample YAML content
        print(f"\n4️⃣ Sample YAML content (original flows only):")
        try:
            with open("original_flows_only.yaml", 'r') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:20]):  # Show first 20 lines
                    print(f"   {line.rstrip()}")
                if len(lines) > 20:
                    print(f"   ... ({len(lines) - 20} more lines)")
        except Exception as e:
            print(f"   ❌ Error reading YAML file: {e}")
            
    except Exception as e:
        print(f"❌ Error in YAML export test: {e}")
        import traceback
        traceback.print_exc()

def test_yaml_format():
    """Test the YAML format with sample data."""
    
    print(f"\n📋 Testing YAML Format")
    print("=" * 30)
    
    # Create sample flow data
    sample_flows = {
        "flow_1": {
            "Source IP": "***********, ***********",
            "Destination IP": "***********0, ***************", 
            "Port": "443",
            "Service": "https, ssh",
            "Action": "Allow",
            "File Type": "pdf"
        },
        "flow_1_a": {  # This should be filtered out
            "Source IP": "***********",
            "Destination IP": "***********0",
            "Port": "443", 
            "Service": "https",
            "Action": "Allow",
            "File Type": "pdf"
        },
        "flow_2": {
            "Source IP": "***********",
            "Destination IP": "***********0",
            "Port": "22, 80, 443",
            "Service": "ssh, http, https", 
            "Action": "Allow",
            "File Type": "exe, dll"
        }
    }
    
    print(f"Sample data: {len(sample_flows)} flows")
    
    try:
        yaml_file = export_original_flows_to_yaml(
            sample_flows,
            "sample_original_flows.yaml",
            include_expanded=False
        )
        
        print(f"✅ Sample YAML created: {yaml_file}")
        
        # Show the content
        print(f"\nGenerated YAML content:")
        with open(yaml_file, 'r') as f:
            content = f.read()
            print(content)
            
    except Exception as e:
        print(f"❌ Error creating sample YAML: {e}")
        import traceback
        traceback.print_exc()

def test_manual_export():
    """Test manual YAML export from CSV file."""
    
    print(f"\n🔧 Manual YAML Export Test")
    print("=" * 35)
    
    csv_file = "test_comma_expansion.csv"
    
    try:
        # Load CSV without expansion to get original entries
        print(f"Loading {csv_file} without expansion...")
        original_data = csv_to_dict_simple(csv_file, expand_comma_separated=False)
        
        print(f"Original entries: {len(original_data)}")
        for flow_id, flow_data in original_data.items():
            print(f"  {flow_id}: {flow_data}")
        
        # Export to YAML
        yaml_file = export_original_flows_to_yaml(
            original_data,
            "manual_export.yaml", 
            include_expanded=True  # Include all since these are originals
        )
        
        print(f"\n✅ Manual export completed: {yaml_file}")
        
    except Exception as e:
        print(f"❌ Error in manual export: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 YAML Export Testing")
    print("=" * 50)
    
    # Test 1: Sample format
    test_yaml_format()
    
    # Test 2: Real CSV export
    test_yaml_export()
    
    # Test 3: Manual export
    test_manual_export()
    
    print(f"\n💡 YAML Export Summary:")
    print(f"   • export_original_flows_to_yaml() creates YAML with your preferred format")
    print(f"   • Source, Destination, Port, Service, Action, File_Type fields")
    print(f"   • Filters out expanded flows (_a, _b, etc.) by default")
    print(f"   • Works with or without PyYAML library")
    print(f"   • Automatically called during CSV validation process")
