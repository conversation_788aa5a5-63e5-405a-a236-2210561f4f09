"""
Tufin API Client for SecureTrack and SecureChange Integration

This module provides a comprehensive client for interacting with Tufin's APIs
to manage firewall rules, check expiration dates, and handle recertification tickets.
"""

import requests
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin
import urllib3

# Disable SSL warnings for self-signed certificates (common in enterprise environments)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TufinAPIError(Exception):
    """Custom exception for Tufin API errors"""
    pass

class TufinAPIClient:
    """
    Tufin API Client for SecureTrack and SecureChange operations
    
    This client handles authentication, API calls, and error handling for
    Tufin SecureTrack and SecureChange platforms.
    """
    
    def __init__(self, base_url: str, username: str, password: str, 
                 verify_ssl: bool = False, timeout: int = 30):
        """
        Initialize the Tufin API client
        
        Args:
            base_url: Base URL for Tufin server (e.g., https://tufin.company.com)
            username: API username
            password: API password
            verify_ssl: Whether to verify SSL certificates (default: False)
            timeout: Request timeout in seconds (default: 30)
        """
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.verify_ssl = verify_ssl
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = verify_ssl
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
        # Authentication token storage
        self._auth_token = None
        self._token_expires = None
        
        # API endpoints
        self.securetrack_api = f"{self.base_url}/securetrack/api"
        self.securechange_api = f"{self.base_url}/securechange/api"
        
    def authenticate(self) -> bool:
        """
        Authenticate with Tufin API and obtain access token
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            auth_url = f"{self.securetrack_api}/authentication/authenticate"
            
            auth_data = {
                "username": self.username,
                "password": self.password
            }
            
            response = self.session.post(
                auth_url,
                json=auth_data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                auth_result = response.json()
                self._auth_token = auth_result.get('token')
                
                # Set token expiration (typically 24 hours)
                self._token_expires = datetime.now() + timedelta(hours=23)
                
                # Add token to session headers
                self.session.headers.update({
                    'Authorization': f'Bearer {self._auth_token}',
                    'Content-Type': 'application/json'
                })
                
                self.logger.info("Successfully authenticated with Tufin API")
                return True
            else:
                self.logger.error(f"Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Authentication error: {str(e)}")
            return False
    
    def _ensure_authenticated(self) -> bool:
        """
        Ensure we have a valid authentication token
        
        Returns:
            bool: True if authenticated, False otherwise
        """
        if not self._auth_token or not self._token_expires:
            return self.authenticate()
        
        # Check if token is about to expire (refresh 1 hour before expiration)
        if datetime.now() >= (self._token_expires - timedelta(hours=1)):
            return self.authenticate()
        
        return True
    
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Make an authenticated API request
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: Full URL for the request
            **kwargs: Additional arguments for requests
            
        Returns:
            requests.Response: The response object
            
        Raises:
            TufinAPIError: If authentication fails or request fails
        """
        if not self._ensure_authenticated():
            raise TufinAPIError("Failed to authenticate with Tufin API")
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # Handle common HTTP errors
            if response.status_code == 401:
                # Token might have expired, try to re-authenticate
                if self.authenticate():
                    response = self.session.request(
                        method=method,
                        url=url,
                        timeout=self.timeout,
                        **kwargs
                    )
                else:
                    raise TufinAPIError("Authentication failed")
            
            if response.status_code >= 400:
                raise TufinAPIError(f"API request failed: {response.status_code} - {response.text}")
            
            return response
            
        except requests.exceptions.RequestException as e:
            raise TufinAPIError(f"Request failed: {str(e)}")
    
    def get_devices(self, domain_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get list of devices managed by SecureTrack, optionally filtered by domain

        Args:
            domain_id: Optional domain ID to filter devices (e.g., 1 for Domain 1)

        Returns:
            List[Dict]: List of device information
        """
        url = f"{self.securetrack_api}/devices"
        params = {}

        if domain_id is not None:
            params['domain'] = domain_id
            self.logger.info(f"Filtering devices for domain ID: {domain_id}")

        response = self._make_request('GET', url, params=params if params else None)
        devices = response.json().get('devices', [])

        if domain_id is not None:
            self.logger.info(f"Found {len(devices)} devices in domain {domain_id}")
        else:
            self.logger.info(f"Found {len(devices)} devices across all domains")

        return devices

    def get_domains(self) -> List[Dict[str, Any]]:
        """
        Get list of all domains in SecureTrack

        Returns:
            List[Dict]: List of domain information with id, name, and description
        """
        url = f"{self.securetrack_api}/domains"
        try:
            response = self._make_request('GET', url)
            domains = response.json().get('domains', [])
            self.logger.info(f"Found {len(domains)} domains")
            return domains
        except Exception as e:
            self.logger.warning(f"Could not retrieve domains: {str(e)}")
            # Return empty list if domains endpoint is not available
            return []

    def get_device_rules(self, device_id: int, include_disabled: bool = False) -> List[Dict[str, Any]]:
        """
        Get rules for a specific device
        
        Args:
            device_id: ID of the device
            include_disabled: Whether to include disabled rules
            
        Returns:
            List[Dict]: List of rules for the device
        """
        url = f"{self.securetrack_api}/devices/{device_id}/rules"
        params = {'include_disabled': str(include_disabled).lower()}
        
        response = self._make_request('GET', url, params=params)
        return response.json().get('rules', [])
    
    def get_rules_with_expiration(self, days_ahead: int = 30, domain_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get rules that have expiration dates within the specified number of days

        Args:
            days_ahead: Number of days to look ahead for expiring rules (default: 30)
            domain_id: Optional domain ID to filter devices (e.g., 1 for Domain 1)

        Returns:
            List[Dict]: List of rules with expiration information
        """
        expiring_rules = []
        target_date = datetime.now() + timedelta(days=days_ahead)

        try:
            # Get devices (filtered by domain if specified)
            devices = self.get_devices(domain_id=domain_id)

            for device in devices:
                device_id = device.get('id')
                device_name = device.get('name', 'Unknown')

                self.logger.info(f"Checking rules for device: {device_name} (ID: {device_id})")

                # Get rules for this device
                rules = self.get_device_rules(device_id, include_disabled=False)

                for rule in rules:
                    rule_expiration = self._parse_rule_expiration(rule)
                    certification_expiration = self._parse_certification_expiration(rule)

                    # Check if rule expires within the target timeframe
                    if rule_expiration and rule_expiration <= target_date:
                        expiring_rules.append({
                            'device_id': device_id,
                            'device_name': device_name,
                            'rule_id': rule.get('id'),
                            'rule_name': rule.get('name', ''),
                            'rule_number': rule.get('number'),
                            'expiration_date': rule_expiration,
                            'expiration_type': 'rule',
                            'days_until_expiration': (rule_expiration - datetime.now()).days,
                            'rule_owner': rule.get('owner', ''),
                            'rule_data': rule
                        })

                    # Check if certification expires within the target timeframe
                    if certification_expiration and certification_expiration <= target_date:
                        expiring_rules.append({
                            'device_id': device_id,
                            'device_name': device_name,
                            'rule_id': rule.get('id'),
                            'rule_name': rule.get('name', ''),
                            'rule_number': rule.get('number'),
                            'expiration_date': certification_expiration,
                            'expiration_type': 'certification',
                            'days_until_expiration': (certification_expiration - datetime.now()).days,
                            'rule_owner': rule.get('owner', ''),
                            'rule_data': rule
                        })

            self.logger.info(f"Found {len(expiring_rules)} rules with upcoming expirations")
            return expiring_rules

        except Exception as e:
            self.logger.error(f"Error checking rule expirations: {str(e)}")
            raise TufinAPIError(f"Failed to check rule expirations: {str(e)}")

    def _parse_rule_expiration(self, rule: Dict[str, Any]) -> Optional[datetime]:
        """
        Parse rule expiration date from rule data

        Args:
            rule: Rule data from API

        Returns:
            datetime: Expiration date if found, None otherwise
        """
        # Common fields where expiration might be stored
        expiration_fields = ['expiration_date', 'expires', 'expiry_date', 'valid_until']

        for field in expiration_fields:
            if field in rule and rule[field]:
                try:
                    # Handle different date formats
                    date_str = str(rule[field])

                    # Try common date formats
                    date_formats = [
                        '%Y-%m-%d',
                        '%Y-%m-%dT%H:%M:%S',
                        '%Y-%m-%d %H:%M:%S',
                        '%m/%d/%Y',
                        '%d/%m/%Y'
                    ]

                    for fmt in date_formats:
                        try:
                            return datetime.strptime(date_str, fmt)
                        except ValueError:
                            continue

                except Exception:
                    continue

        return None

    def _parse_certification_expiration(self, rule: Dict[str, Any]) -> Optional[datetime]:
        """
        Parse certification expiration date from rule data

        Args:
            rule: Rule data from API

        Returns:
            datetime: Certification expiration date if found, None otherwise
        """
        # Look for certification-related fields
        cert_fields = ['certification_expiry', 'cert_expires', 'recertification_date', 'last_certified']

        for field in cert_fields:
            if field in rule and rule[field]:
                try:
                    date_str = str(rule[field])

                    # If this is last_certified, add certification period (typically 1 year)
                    if field == 'last_certified':
                        try:
                            last_cert = datetime.strptime(date_str, '%Y-%m-%d')
                            return last_cert + timedelta(days=365)  # Assume 1-year certification period
                        except ValueError:
                            continue

                    # Try to parse direct expiration dates
                    date_formats = [
                        '%Y-%m-%d',
                        '%Y-%m-%dT%H:%M:%S',
                        '%Y-%m-%d %H:%M:%S',
                        '%m/%d/%Y',
                        '%d/%m/%Y'
                    ]

                    for fmt in date_formats:
                        try:
                            return datetime.strptime(date_str, fmt)
                        except ValueError:
                            continue

                except Exception:
                    continue

        return None

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to Tufin API

        Returns:
            Dict: Connection test results
        """
        try:
            if self.authenticate():
                # Try to get basic system info
                url = f"{self.securetrack_api}/system/info"
                response = self._make_request('GET', url)

                return {
                    'success': True,
                    'message': 'Successfully connected to Tufin API',
                    'system_info': response.json()
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to authenticate with Tufin API'
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'Connection test failed: {str(e)}'
            }
