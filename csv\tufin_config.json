{"tufin": {"base_url": "https://your-tufin-server.company.com", "username": "your-username", "password": "your-password", "verify_ssl": false, "timeout": 30}, "recertification": {"days_ahead": 30, "auto_assign_tickets": true, "only_create_tickets_with_owners": false, "default_owner": "<EMAIL>", "domain_filter": {"enabled": false, "domain_id": 1, "domain_name": "Domain 1"}}, "owner_mapping": {"DMZ": "<EMAIL>", "WEB": "<EMAIL>", "DB": "<EMAIL>", "MAIL": "<EMAIL>", "VPN": "<EMAIL>"}, "ticket_template": {"workflow": "rule_recertification", "priority": "medium", "subject_template": "Rule Recertification Required: {rule_name} on {device_name}", "description_template": "Rule Recertification Required\n\nDevice: {device_name}\nRule ID: {rule_id}\nRule Name: {rule_name}\nRule Number: {rule_number}\nExpiration Type: {expiration_type}\nExpiration Date: {expiration_date}\nDays Until Expiration: {days_until_expiration}\n\nThis rule requires recertification before its expiration date.\nPlease review the rule and confirm it is still required.\n\nActions Required:\n1. Review the rule configuration\n2. Verify the rule is still necessary\n3. Update the rule if needed\n4. Certify the rule for continued use\n\nIf the rule is no longer needed, please disable or remove it."}, "logging": {"log_level": "INFO", "log_file": "tufin_recertification.log", "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "reporting": {"report_file": "tufin_recertification_report.md", "include_rule_details": true, "max_rules_in_report": 50}, "notifications": {"email_enabled": false, "email_smtp_server": "smtp.company.com", "email_smtp_port": 587, "email_username": "<EMAIL>", "email_password": "email-password", "email_recipients": ["<EMAIL>", "<EMAIL>"], "email_subject": "Tufin Rule Recertification Report - {date}"}}