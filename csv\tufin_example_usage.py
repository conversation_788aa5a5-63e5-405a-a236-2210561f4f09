#!/usr/bin/env python3
"""
Example usage of the Tufin Rule Recertification system

This script demonstrates how to use the Tufin recertification components
individually and as a complete system.
"""

import json
import logging
from datetime import datetime

# Import our modules
from tufin_api_client import TufinAPIClient, TufinAPIError
from securechange_ticket_manager import SecureChangeTicketManager
from rule_owner_manager import RuleOwnerManager
from tufin_rule_recertification import TufinRecertificationManager

def example_api_client():
    """Example: Basic API client usage"""
    print("=== API Client Example ===")
    
    # Initialize client
    client = TufinAPIClient(
        base_url="https://your-tufin-server.com",
        username="your-username",
        password="your-password",
        verify_ssl=False
    )
    
    # Test connection
    print("Testing connection...")
    result = client.test_connection()
    if result['success']:
        print("✅ Connection successful!")
        print(f"System info: {result.get('system_info', {}).get('version', 'Unknown')}")
    else:
        print(f"❌ Connection failed: {result['message']}")
        return
    
    # Get devices
    print("\nGetting devices...")
    try:
        devices = client.get_devices()
        print(f"Found {len(devices)} devices")
        for device in devices[:3]:  # Show first 3
            print(f"  - {device.get('name', 'Unknown')} (ID: {device.get('id')})")
    except TufinAPIError as e:
        print(f"Error getting devices: {e}")
    
    # Check for expiring rules
    print("\nChecking for expiring rules...")
    try:
        expiring_rules = client.get_rules_with_expiration(days_ahead=30)
        print(f"Found {len(expiring_rules)} expiring rules")
        
        for rule in expiring_rules[:3]:  # Show first 3
            print(f"  - Rule {rule.get('rule_id')} on {rule.get('device_name')}")
            print(f"    Expires: {rule.get('expiration_date')} ({rule.get('days_until_expiration')} days)")
            print(f"    Type: {rule.get('expiration_type')}")
            print(f"    Owner: {rule.get('rule_owner', 'Unknown')}")
    except TufinAPIError as e:
        print(f"Error checking expiring rules: {e}")

def example_configuration():
    """Example: Working with configuration"""
    print("\n=== Configuration Example ===")
    
    # Example configuration structure
    config = {
        "tufin": {
            "base_url": "https://tufin.company.com",
            "username": "api-user",
            "password": "secure-password",
            "verify_ssl": False,
            "timeout": 30
        },
        "recertification": {
            "days_ahead": 30,
            "auto_assign_tickets": True,
            "only_create_tickets_with_owners": False,
            "default_owner": "<EMAIL>"
        },
        "owner_mapping": {
            "DMZ": "<EMAIL>",
            "WEB": "<EMAIL>",
            "DATABASE": "<EMAIL>",
            "MAIL": "<EMAIL>"
        },
        "ticket_template": {
            "workflow": "rule_recertification",
            "priority": "medium",
            "subject_template": "URGENT: Rule {rule_name} expires in {days_until_expiration} days",
            "description_template": "Rule {rule_name} on device {device_name} requires immediate recertification."
        },
        "logging": {
            "log_level": "INFO",
            "log_file": "tufin_recertification.log"
        }
    }
    
    # Save configuration to file
    config_file = "my_tufin_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Configuration saved to: {config_file}")
    print("You can now run the script with:")
    print(f"python tufin_rule_recertification.py --config {config_file}")

def main():
    """Run all examples"""
    print("Tufin Rule Recertification - Usage Examples")
    print("=" * 50)
    
    # Set up basic logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    try:
        # Configuration example (safe to run)
        example_configuration()
        
        # API example (requires real Tufin server - commented out)
        print("\n" + "=" * 50)
        print("API Example (requires real Tufin server):")
        print("Uncomment example_api_client() call to test with real server")
        # example_api_client()
        
        print("\n" + "=" * 50)
        print("Examples completed!")
        print("\nNext steps:")
        print("1. Edit my_tufin_config.json with your Tufin server details")
        print("2. Test connection: python -c \"from tufin_api_client import TufinAPIClient; print('Import successful')\"")
        print("3. Run: python tufin_rule_recertification.py --config my_tufin_config.json --dry-run")
        print("4. For production: python tufin_rule_recertification.py --config my_tufin_config.json")
        
    except Exception as e:
        print(f"Example failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
