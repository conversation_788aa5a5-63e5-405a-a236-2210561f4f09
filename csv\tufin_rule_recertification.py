#!/usr/bin/env python3
"""
Tufin Rule Recertification Automation Script

This script automates the process of:
1. Checking for rules with expiration dates in the next 30 days
2. Identifying rules with expiring certification status
3. Creating SecureChange recertification tickets
4. Automatically assigning tickets to rule owners

Usage:
    python tufin_rule_recertification.py --config config.json
    python tufin_rule_recertification.py --url https://tufin.company.com --username admin --password secret
"""

import argparse
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from tufin_api_client import TufinAPIClient, TufinAPIError
from securechange_ticket_manager import SecureChangeTicketManager
from rule_owner_manager import RuleOwnerManager

class TufinRecertificationManager:
    """
    Main manager class for Tufin rule recertification automation
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the recertification manager
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.setup_logging()
        
        # Initialize API client
        self.api_client = TufinAPIClient(
            base_url=config['tufin']['base_url'],
            username=config['tufin']['username'],
            password=config['tufin']['password'],
            verify_ssl=config['tufin'].get('verify_ssl', False),
            timeout=config['tufin'].get('timeout', 30)
        )
        
        # Initialize managers
        self.ticket_manager = SecureChangeTicketManager(self.api_client)
        self.owner_manager = RuleOwnerManager(
            self.api_client,
            default_owner=config.get('default_owner'),
            owner_mapping=config.get('owner_mapping', {})
        )
        
        self.logger = logging.getLogger(__name__)
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = self.config.get('log_level', 'INFO').upper()
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(
                    self.config.get('log_file', 'tufin_recertification.log')
                )
            ]
        )
    
    def run_recertification_process(self) -> Dict[str, Any]:
        """
        Run the complete recertification process
        
        Returns:
            Dict: Process results and statistics
        """
        self.logger.info("Starting Tufin rule recertification process")
        
        results = {
            'start_time': datetime.now().isoformat(),
            'success': False,
            'error': None,
            'statistics': {},
            'expiring_rules': [],
            'ticket_results': {},
            'owner_validation': {}
        }
        
        try:
            # Step 1: Test API connection
            self.logger.info("Testing Tufin API connection...")
            connection_test = self.api_client.test_connection()
            if not connection_test['success']:
                raise TufinAPIError(f"API connection failed: {connection_test['message']}")
            
            self.logger.info("✅ Successfully connected to Tufin API")
            
            # Step 2: Find expiring rules
            self.logger.info("Checking for expiring rules...")
            days_ahead = self.config.get('days_ahead', 30)

            # Check for domain filtering
            domain_filter = self.config.get('domain_filter', {})
            domain_id = None
            if domain_filter.get('enabled', False):
                domain_id = domain_filter.get('domain_id')
                domain_name = domain_filter.get('domain_name', f'Domain {domain_id}')
                self.logger.info(f"🎯 Filtering rules for {domain_name} (ID: {domain_id})")

            expiring_rules = self.api_client.get_rules_with_expiration(days_ahead, domain_id=domain_id)
            
            results['expiring_rules'] = expiring_rules
            results['statistics']['total_expiring_rules'] = len(expiring_rules)
            
            if not expiring_rules:
                self.logger.info("No expiring rules found")
                results['success'] = True
                return results
            
            self.logger.info(f"Found {len(expiring_rules)} expiring rules")
            
            # Step 3: Validate rule owners
            self.logger.info("Validating rule owners...")
            owner_validation = self.owner_manager.bulk_validate_owners(expiring_rules)
            results['owner_validation'] = owner_validation
            
            # Step 4: Create recertification tickets
            self.logger.info("Creating recertification tickets...")
            
            # Only create tickets for rules with valid owners (if configured)
            rules_for_tickets = expiring_rules
            if self.config.get('only_create_tickets_with_owners', False):
                rules_for_tickets = owner_validation['rules_with_valid_owners']
                self.logger.info(f"Creating tickets only for {len(rules_for_tickets)} rules with valid owners")
            
            ticket_results = self.ticket_manager.bulk_create_recertification_tickets(
                rules_for_tickets,
                auto_assign=self.config.get('auto_assign_tickets', True)
            )
            
            results['ticket_results'] = ticket_results
            
            # Step 5: Generate summary
            self._generate_summary(results)
            
            results['success'] = True
            self.logger.info("✅ Recertification process completed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Recertification process failed: {str(e)}")
            results['error'] = str(e)
            results['success'] = False
        
        finally:
            results['end_time'] = datetime.now().isoformat()
        
        return results
    
    def _generate_summary(self, results: Dict[str, Any]):
        """Generate and log process summary"""
        stats = results.get('statistics', {})
        ticket_results = results.get('ticket_results', {})
        owner_validation = results.get('owner_validation', {})
        
        self.logger.info("\n" + "="*60)
        self.logger.info("RECERTIFICATION PROCESS SUMMARY")
        self.logger.info("="*60)
        
        # Rules summary
        total_rules = stats.get('total_expiring_rules', 0)
        self.logger.info(f"📋 Total expiring rules found: {total_rules}")
        
        # Owner validation summary
        if owner_validation:
            validation_summary = owner_validation.get('validation_summary', {})
            rules_with_owners = validation_summary.get('rules_with_owners', 0)
            rules_without_owners = validation_summary.get('rules_without_owners', 0)
            unique_owners = validation_summary.get('unique_owners', 0)
            
            self.logger.info(f"👥 Rules with valid owners: {rules_with_owners}")
            self.logger.info(f"❓ Rules without owners: {rules_without_owners}")
            self.logger.info(f"🔢 Unique owners identified: {unique_owners}")
        
        # Ticket creation summary
        if ticket_results:
            successful_tickets = len(ticket_results.get('successful_tickets', []))
            failed_tickets = len(ticket_results.get('failed_tickets', []))
            successful_assignments = len([r for r in ticket_results.get('assignment_results', []) if r.get('success')])
            
            self.logger.info(f"🎫 Tickets created successfully: {successful_tickets}")
            self.logger.info(f"❌ Tickets failed to create: {failed_tickets}")
            self.logger.info(f"📌 Tickets assigned successfully: {successful_assignments}")
        
        self.logger.info("="*60)
    
    def generate_report(self, results: Dict[str, Any], output_file: Optional[str] = None) -> str:
        """
        Generate a detailed report of the recertification process
        
        Args:
            results: Process results
            output_file: Optional output file path
            
        Returns:
            str: Report content
        """
        report_lines = [
            "# Tufin Rule Recertification Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## Process Summary",
            f"- Start Time: {results.get('start_time', 'Unknown')}",
            f"- End Time: {results.get('end_time', 'Unknown')}",
            f"- Success: {'✅ Yes' if results.get('success') else '❌ No'}",
        ]
        
        if results.get('error'):
            report_lines.extend([
                f"- Error: {results['error']}",
                ""
            ])
        
        # Statistics
        stats = results.get('statistics', {})
        if stats:
            report_lines.extend([
                "## Statistics",
                f"- Total expiring rules: {stats.get('total_expiring_rules', 0)}",
                ""
            ])
        
        # Expiring rules details
        expiring_rules = results.get('expiring_rules', [])
        if expiring_rules:
            report_lines.extend([
                "## Expiring Rules",
                "| Device | Rule ID | Rule Name | Expiration Type | Days Until Expiration | Owner |",
                "|--------|---------|-----------|-----------------|----------------------|-------|"
            ])
            
            for rule in expiring_rules[:20]:  # Limit to first 20 for readability
                device_name = rule.get('device_name', 'Unknown')
                rule_id = rule.get('rule_id', 'Unknown')
                rule_name = rule.get('rule_name', 'Unknown')[:30]  # Truncate long names
                exp_type = rule.get('expiration_type', 'Unknown')
                days_until = rule.get('days_until_expiration', 'Unknown')
                owner = rule.get('rule_owner', 'Unknown')
                
                report_lines.append(f"| {device_name} | {rule_id} | {rule_name} | {exp_type} | {days_until} | {owner} |")
            
            if len(expiring_rules) > 20:
                report_lines.append(f"| ... and {len(expiring_rules) - 20} more rules ... |")
            
            report_lines.append("")
        
        # Ticket results
        ticket_results = results.get('ticket_results', {})
        if ticket_results:
            successful_tickets = ticket_results.get('successful_tickets', [])
            failed_tickets = ticket_results.get('failed_tickets', [])
            
            report_lines.extend([
                "## Ticket Creation Results",
                f"- Successful tickets: {len(successful_tickets)}",
                f"- Failed tickets: {len(failed_tickets)}",
                ""
            ])
            
            if successful_tickets:
                report_lines.extend([
                    "### Successfully Created Tickets",
                    "| Ticket ID | Rule ID | Device | Assignee |",
                    "|-----------|---------|--------|----------|"
                ])
                
                for ticket in successful_tickets[:10]:  # Limit for readability
                    ticket_id = ticket.get('ticket_id', 'Unknown')
                    rule_info = ticket.get('rule_info', {})
                    rule_id = rule_info.get('rule_id', 'Unknown')
                    device_name = rule_info.get('device_name', 'Unknown')
                    
                    # Find assignment result
                    assignee = 'Not assigned'
                    assignment_results = ticket_results.get('assignment_results', [])
                    for assignment in assignment_results:
                        if assignment.get('ticket_id') == ticket_id and assignment.get('success'):
                            assignee = assignment.get('assignee', 'Unknown')
                            break
                    
                    report_lines.append(f"| {ticket_id} | {rule_id} | {device_name} | {assignee} |")
                
                report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        # Save to file if requested
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            self.logger.info(f"Report saved to: {output_file}")
        
        return report_content


def load_config(config_file: str) -> Dict[str, Any]:
    """Load configuration from JSON file"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"Configuration file not found: {config_file}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in configuration file: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Tufin Rule Recertification Automation")
    
    # Configuration options
    config_group = parser.add_mutually_exclusive_group(required=True)
    config_group.add_argument('--config', help='Path to configuration JSON file')
    config_group.add_argument('--url', help='Tufin server URL')
    
    # Direct configuration options (when not using config file)
    parser.add_argument('--username', help='Tufin username')
    parser.add_argument('--password', help='Tufin password')
    parser.add_argument('--days-ahead', type=int, default=30, help='Days ahead to check for expiring rules')
    parser.add_argument('--default-owner', help='Default owner for rules without specific owners')
    parser.add_argument('--report-file', help='Output file for detailed report')
    parser.add_argument('--dry-run', action='store_true', help='Run without creating tickets (testing mode)')

    # Domain filtering options
    parser.add_argument('--domain-id', type=int, help='Filter rules to specific domain ID (e.g., 1 for Domain 1)')
    parser.add_argument('--list-domains', action='store_true', help='List available domains and exit')
    
    args = parser.parse_args()
    
    # Load or build configuration
    if args.config:
        config = load_config(args.config)
    else:
        if not args.username or not args.password:
            parser.error("When not using --config, both --username and --password are required")
        
        config = {
            'tufin': {
                'base_url': args.url,
                'username': args.username,
                'password': args.password,
                'verify_ssl': False
            },
            'days_ahead': args.days_ahead,
            'default_owner': args.default_owner,
            'auto_assign_tickets': True,
            'only_create_tickets_with_owners': False,
            'log_level': 'INFO'
        }
    
    # Override config with command line arguments
    if args.days_ahead != 30:
        config['days_ahead'] = args.days_ahead
    if args.default_owner:
        config['default_owner'] = args.default_owner
    if args.dry_run:
        config['dry_run'] = True

    # Handle domain filtering
    if args.domain_id:
        if 'domain_filter' not in config:
            config['domain_filter'] = {}
        config['domain_filter']['enabled'] = True
        config['domain_filter']['domain_id'] = args.domain_id
        config['domain_filter']['domain_name'] = f'Domain {args.domain_id}'

    # Handle list domains request
    if args.list_domains:
        try:
            from tufin_api_client import TufinAPIClient

            tufin_config = config.get('tufin', {})
            api_client = TufinAPIClient(
                base_url=tufin_config['base_url'],
                username=tufin_config['username'],
                password=tufin_config['password'],
                verify_ssl=tufin_config.get('verify_ssl', True),
                timeout=tufin_config.get('timeout', 30)
            )

            print("🌐 Available Tufin Domains:")
            print("=" * 50)

            domains = api_client.get_domains()
            if domains:
                for domain in domains:
                    domain_id = domain.get('id', 'N/A')
                    domain_name = domain.get('name', 'Unknown')
                    domain_desc = domain.get('description', '')
                    print(f"  ID: {domain_id:<3} | Name: {domain_name:<20} | {domain_desc}")

                print("\n💡 Usage examples:")
                print(f"  # Filter to Domain 1:")
                print(f"  python {sys.argv[0]} --config your_config.json --domain-id 1")
                print(f"  # Or add to config file:")
                print(f'  "domain_filter": {{"enabled": true, "domain_id": 1}}')
            else:
                print("  No domains found or domains endpoint not available")

        except Exception as e:
            print(f"❌ Error retrieving domains: {str(e)}")
            sys.exit(1)

        sys.exit(0)

    try:
        # Run the recertification process
        manager = TufinRecertificationManager(config)
        results = manager.run_recertification_process()
        
        # Generate report
        report_file = args.report_file or config.get('report_file')
        if report_file:
            manager.generate_report(results, report_file)
        
        # Exit with appropriate code
        sys.exit(0 if results['success'] else 1)
        
    except Exception as e:
        print(f"Fatal error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
