"""
Example of how to use the CSV processing function in your script
"""

from csv_processor import csv_to_dict_simple


def main():
    # Path to your CSV file
    csv_file = "Example.csv"

    # Get all flow rows as a dictionary - this is now your main dictionary!
    flow_data = csv_to_dict_simple(csv_file)

    # The flow_data is already a dictionary with flow_X keys
    # You can use it directly in your script logic

    # Iterate through each flow
    for flow_key, flow_row in flow_data.items():
        # Example: Access individual values
        source_ip = flow_row.get('Source IP')
        dest_ip = flow_row.get('Destination IP')
        port = flow_row.get('Port')
        service = flow_row.get('Service')
        action = flow_row.get('Action')

        print(f"Processing {flow_key}:")
        print(f"  Source: {source_ip} -> Destination: {dest_ip}")
        print(f"  Port: {port}, Service: {service}, Action: {action}")
        print()

    # Now you have all your data in the flow_data dictionary
    print("All processed flows:")
    for flow_id, flow_info in flow_data.items():
        print(f"{flow_id}: {flow_info}")

    # Example: Further processing based on specific criteria
    print("\nFiltering examples:")

    # Find all HTTPS flows
    https_flows = {k: v for k, v in flow_data.items()
                   if v.get('Service') == 'https'}
    print(f"HTTPS flows: {len(https_flows)}")

    # Find flows to specific IP range
    target_flows = {k: v for k, v in flow_data.items()
                    if v.get('Destination IP', '').startswith('**********')}
    print(f"Flows to **********xx: {len(target_flows)}")

    return flow_data


if __name__ == "__main__":
    result = main()
