#!/usr/bin/env python3
"""
CSV Security Validator with Row Skipping
Skips the first 31 rows when importing CSV files and validates file types including comma-separated values.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator, generate_markdown_report

def validate_csv_skip_31_rows(csv_file: str):
    """
    Validate a CSV file by skipping the first 31 rows and checking for file types.
    
    Args:
        csv_file: Path to CSV file
    """
    print(f"🔍 CSV Security Validation (Skip First 31 Rows)")
    print("=" * 60)
    print(f"📁 File: {csv_file}")
    
    try:
        # Load CSV data, skipping first 31 rows
        print(f"📥 Loading CSV (skipping first 31 rows)...")
        flow_data = csv_to_dict_simple(csv_file, skip_rows=31)
        
        if not flow_data:
            print(f"❌ No data loaded. Check if:")
            print(f"   • File exists and is readable")
            print(f"   • File has more than 31 rows")
            print(f"   • Row 32 contains valid column headers")
            print(f"   • Subsequent rows contain 'Flow' data")
            return None
        
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Show detected columns
        if flow_data:
            sample_flow = next(iter(flow_data.values()))
            print(f"📋 Detected columns: {list(sample_flow.keys())}")
            
            # Check for File Type column
            if "File Type" in sample_flow:
                print(f"✅ File Type column detected - will validate file types")
            else:
                print(f"⚠️  No File Type column found")
        
        # Run security validation
        print(f"\n🔍 Running security validation...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        # Display results
        print(f"\n📊 Validation Results:")
        print(f"  • Total flows: {results['total_flows']}")
        print(f"  • Flows with issues: {results['flows_with_issues']}")
        print(f"  • Critical issues: {results['critical_issues']} 🔴")
        print(f"  • High risk issues: {results['high_risk_issues']} 🟠")
        print(f"  • Medium risk issues: {results['medium_risk_issues']} 🟡")
        print(f"  • Low risk issues: {results['low_risk_issues']} 🟢")
        
        # File type analysis
        if any("File Type" in flow_result.get("flow_data", {}) 
               for flow_result in results["flow_results"].values()):
            
            print(f"\n📁 File Type Analysis:")
            print("-" * 40)
            
            file_type_stats = {}
            high_risk_detections = []
            
            for flow_id, flow_result in results["flow_results"].items():
                flow_data_item = flow_result.get("flow_data", {})
                file_type_field = flow_data_item.get("File Type", "")
                
                if file_type_field:
                    # Handle comma-separated file types
                    if "," in file_type_field:
                        individual_types = [ft.strip().lower() for ft in file_type_field.split(",")]
                        print(f"  📋 {flow_id}: Multiple types - {file_type_field}")
                    else:
                        individual_types = [file_type_field.strip().lower()]
                        print(f"  📋 {flow_id}: Single type - {file_type_field}")
                    
                    # Count file types
                    for file_type in individual_types:
                        if file_type:
                            file_type_stats[file_type] = file_type_stats.get(file_type, 0) + 1
                    
                    # Check for high-risk file type issues
                    file_type_issues = [issue for issue in flow_result.get("issues", []) 
                                      if issue.get("field") == "File Type" and 
                                      issue.get("risk_level") in ["critical", "high"]]
                    
                    if file_type_issues:
                        source_ip = flow_data_item.get("Source IP", "unknown")
                        dest_ip = flow_data_item.get("Destination IP", "unknown")
                        service = flow_data_item.get("Service", "unknown")
                        
                        for issue in file_type_issues:
                            high_risk_detections.append({
                                "flow_id": flow_id,
                                "file_type": issue.get("value", "unknown"),
                                "risk_level": issue.get("risk_level", "unknown"),
                                "source_ip": source_ip,
                                "dest_ip": dest_ip,
                                "service": service,
                                "message": issue.get("message", "")
                            })
            
            # Show file type summary
            if file_type_stats:
                print(f"\n📊 File Type Summary:")
                for file_type, count in sorted(file_type_stats.items()):
                    print(f"  • {file_type}: {count} occurrences")
            
            # Show high-risk detections
            if high_risk_detections:
                print(f"\n🚨 High-Risk File Type Detections:")
                print("-" * 45)
                for detection in high_risk_detections:
                    risk_emoji = "🔴" if detection["risk_level"] == "critical" else "🟠"
                    print(f"  {risk_emoji} {detection['flow_id']}: {detection['file_type']} file")
                    print(f"     {detection['source_ip']} → {detection['dest_ip']} via {detection['service']}")
                    print(f"     Risk: {detection['risk_level'].upper()}")
                    print(f"     {detection['message']}")
                    print()
            else:
                print(f"\n✅ No high-risk file types detected")
        
        # Generate detailed report
        print(f"\n📄 Generating detailed report...")
        report_file = generate_markdown_report(results, csv_file)
        print(f"✅ Report saved: {report_file}")
        
        return results
        
    except FileNotFoundError:
        print(f"❌ Error: File '{csv_file}' not found")
        return None
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function - modify the csv_file variable to point to your file."""
    
    # MODIFY THIS LINE to point to your CSV file
    csv_file = "Example_with_31_header_rows.csv"  # Change this to your actual file
    
    print("🚀 CSV Security Validator with Row Skipping")
    print("=" * 70)
    
    results = validate_csv_skip_31_rows(csv_file)
    
    if results:
        print(f"\n✅ Validation completed successfully!")
        print(f"\n💡 Key Features:")
        print(f"  ✅ Skips first 31 rows automatically")
        print(f"  ✅ Detects individual file types in comma-separated lists")
        print(f"  ✅ Validates exe, dll, bat, zip, and other file types")
        print(f"  ✅ Provides risk assessment and security guidance")
        print(f"  ✅ Generates comprehensive markdown report")
        
        print(f"\n📋 File Type Risk Categories:")
        print(f"  🔴 CRITICAL: exe, bat, cmd, scr, pif, com")
        print(f"  🟠 HIGH: dll, sys, vbs, js, jar, msi, iso, img, dmg, pkg")
        print(f"  🟡 MEDIUM: zip, rar, 7z, tar, gz, ps1, sh, py, pl, rb")
        
    else:
        print(f"\n❌ Validation failed!")
        print(f"\n🔧 Troubleshooting:")
        print(f"  • Make sure the CSV file exists")
        print(f"  • Check that row 32 contains column headers")
        print(f"  • Verify that data rows start with 'Flow'")
        print(f"  • Ensure the file has a 'File Type' column for file validation")

if __name__ == "__main__":
    main()
