#!/usr/bin/env python3
"""
Example script for validating CSV files that include File Type columns.
This demonstrates how to detect and validate file types like 'exe'.
"""

from csv_processor import csv_to_dict_simple
from security_validator import SecurityValidator, generate_markdown_report

def validate_csv_with_file_types(csv_file: str):
    """
    Validate a CSV file that includes a File Type column.
    
    Args:
        csv_file: Path to CSV file with File Type column
    """
    print(f"🔍 Validating CSV with File Types: {csv_file}")
    print("=" * 60)
    
    try:
        # Load CSV data
        print(f"📁 Loading CSV file...")
        flow_data = csv_to_dict_simple(csv_file)
        print(f"✅ Loaded {len(flow_data)} flows")
        
        # Show sample data structure
        if flow_data:
            sample_flow = next(iter(flow_data.values()))
            print(f"\n📋 CSV columns detected:")
            for column in sample_flow.keys():
                print(f"  • {column}")
            
            # Check if File Type column exists
            if "File Type" in sample_flow:
                print(f"✅ File Type column found!")
            else:
                print(f"⚠️  File Type column not found")
                print(f"   Available columns: {list(sample_flow.keys())}")
        
        # Run security validation
        print(f"\n🔍 Running security validation...")
        validator = SecurityValidator("unacceptable_values.json", "security_guidance.json")
        results = validator.validate_flow_data(flow_data)
        
        # Show results summary
        print(f"\n📊 Validation Results:")
        print(f"  • Total flows: {results['total_flows']}")
        print(f"  • Flows with issues: {results['flows_with_issues']}")
        print(f"  • Critical issues: {results['critical_issues']} 🔴")
        print(f"  • High risk issues: {results['high_risk_issues']} 🟠")
        print(f"  • Medium risk issues: {results['medium_risk_issues']} 🟡")
        print(f"  • Low risk issues: {results['low_risk_issues']} 🟢")
        
        # Show file type specific results
        print(f"\n📁 File Type Analysis:")
        print("-" * 40)
        
        file_type_summary = {}
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type = flow_data_item.get("File Type", "N/A")
            
            if file_type != "N/A":
                if file_type not in file_type_summary:
                    file_type_summary[file_type] = {
                        "count": 0,
                        "issues": 0,
                        "max_risk": "low"
                    }
                
                file_type_summary[file_type]["count"] += 1
                
                # Check for file type issues
                file_type_issues = [issue for issue in flow_result.get("issues", []) 
                                  if issue.get("field") == "File Type"]
                
                if file_type_issues:
                    file_type_summary[file_type]["issues"] += 1
                    for issue in file_type_issues:
                        risk = issue.get("risk_level", "low")
                        current_max = file_type_summary[file_type]["max_risk"]
                        
                        # Update max risk (critical > high > medium > low)
                        risk_order = {"low": 0, "medium": 1, "high": 2, "critical": 3}
                        if risk_order.get(risk, 0) > risk_order.get(current_max, 0):
                            file_type_summary[file_type]["max_risk"] = risk
        
        # Display file type summary
        for file_type, summary in sorted(file_type_summary.items()):
            count = summary["count"]
            issues = summary["issues"]
            max_risk = summary["max_risk"]
            
            risk_emoji = {
                "critical": "🔴",
                "high": "🟠",
                "medium": "🟡", 
                "low": "🟢"
            }.get(max_risk, "⚪")
            
            status = f"{issues}/{count} with issues" if issues > 0 else "No issues"
            print(f"  {risk_emoji} {file_type}: {count} flows, {status} (Max risk: {max_risk})")
        
        # Show specific high-risk file detections
        print(f"\n⚠️  High-Risk File Type Detections:")
        print("-" * 40)
        
        high_risk_found = False
        for flow_id, flow_result in results["flow_results"].items():
            flow_data_item = flow_result.get("flow_data", {})
            file_type = flow_data_item.get("File Type", "")
            
            # Check for critical or high risk file type issues
            file_type_issues = [issue for issue in flow_result.get("issues", []) 
                              if issue.get("field") == "File Type" and 
                              issue.get("risk_level") in ["critical", "high"]]
            
            if file_type_issues:
                high_risk_found = True
                source_ip = flow_data_item.get("Source IP", "unknown")
                dest_ip = flow_data_item.get("Destination IP", "unknown")
                
                print(f"  🚨 {flow_id}: {file_type} file detected")
                print(f"     Source: {source_ip} → Destination: {dest_ip}")
                
                for issue in file_type_issues:
                    risk_level = issue.get("risk_level", "unknown")
                    message = issue.get("message", "No message")
                    print(f"     Risk: {risk_level.upper()} - {message}")
        
        if not high_risk_found:
            print(f"  ✅ No high-risk file types detected")
        
        # Generate report
        print(f"\n📄 Generating detailed report...")
        report_file = generate_markdown_report(results, csv_file)
        print(f"✅ Report generated: {report_file}")
        
        return results
        
    except FileNotFoundError:
        print(f"❌ Error: CSV file '{csv_file}' not found")
        print(f"   Make sure the file exists and the path is correct")
        return None
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function to demonstrate file type validation."""
    
    print("🚀 CSV File Type Validation")
    print("=" * 50)
    
    # Test with the example file that has file types
    csv_file = "Example_with_filetypes.csv"
    
    results = validate_csv_with_file_types(csv_file)
    
    if results:
        print(f"\n✅ Validation completed successfully!")
        print(f"\n💡 Usage Tips:")
        print(f"  • Add a 'File Type' column to your CSV files")
        print(f"  • The system will automatically detect and validate file types")
        print(f"  • Supported risk levels: critical, high, medium, low")
        print(f"  • Check the generated markdown report for detailed analysis")
        
        print(f"\n📋 File Type Risk Categories:")
        print(f"  🔴 BLOCKED (Critical): exe, bat, cmd, scr, pif, com")
        print(f"  🟠 RISKY (High): dll, sys, vbs, js, jar, msi") 
        print(f"  🟡 CONCERNING (Medium): zip, rar, 7z, tar, gz, ps1, sh, py")
        print(f"  🟠 SUSPICIOUS (High): iso, img, dmg, pkg")
        
    else:
        print(f"\n❌ Validation failed!")

if __name__ == "__main__":
    main()
