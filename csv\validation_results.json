{"total_flows": 4, "flows_with_issues": 4, "critical_issues": 0, "high_risk_issues": 1, "medium_risk_issues": 4, "low_risk_issues": 0, "flow_results": {"flow_1": {"flow_id": "flow_1", "flow_data": {"Source IP": "***********", "Destination IP": "************", "Port": "111", "Service": "https", "Action": "Allow"}, "issues": [{"field": "Action", "value": "Allow", "category": "concerning", "risk_level": "medium", "message": "Action 'Allow' is classified as concerning"}], "guidance": [{"field": "Source IP", "value": "***********", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Destination IP", "value": "************", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Port", "value": "111", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Service", "value": "https", "risk_level": "low", "message": "HTTPS is generally secure but monitor for suspicious destinations.", "recommendations": ["Monitor certificate validity", "Check destination reputation"]}, {"field": "Action", "value": "Allow", "risk_level": "varies", "message": "Traffic is being allowed - ensure this is intentional and secure.", "recommendations": ["Review allow rules regularly", "Monitor allowed traffic", "Implement least privilege"]}], "overall_risk": "medium"}, "flow_2": {"flow_id": "flow_2", "flow_data": {"Source IP": "***********", "Destination IP": "***********0", "Port": "222", "Service": "https", "Action": "Allow"}, "issues": [{"field": "Action", "value": "Allow", "category": "concerning", "risk_level": "medium", "message": "Action 'Allow' is classified as concerning"}], "guidance": [{"field": "Source IP", "value": "***********", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Destination IP", "value": "***********0", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Port", "value": "222", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Service", "value": "https", "risk_level": "low", "message": "HTTPS is generally secure but monitor for suspicious destinations.", "recommendations": ["Monitor certificate validity", "Check destination reputation"]}, {"field": "Action", "value": "Allow", "risk_level": "varies", "message": "Traffic is being allowed - ensure this is intentional and secure.", "recommendations": ["Review allow rules regularly", "Monitor allowed traffic", "Implement least privilege"]}], "overall_risk": "medium"}, "flow_3": {"flow_id": "flow_3", "flow_data": {"Source IP": "***********", "Destination IP": "***********0", "Port": "333", "Service": "https", "Action": "Allow"}, "issues": [{"field": "Action", "value": "Allow", "category": "concerning", "risk_level": "medium", "message": "Action 'Allow' is classified as concerning"}], "guidance": [{"field": "Source IP", "value": "***********", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Destination IP", "value": "***********0", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Port", "value": "333", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Service", "value": "https", "risk_level": "low", "message": "HTTPS is generally secure but monitor for suspicious destinations.", "recommendations": ["Monitor certificate validity", "Check destination reputation"]}, {"field": "Action", "value": "Allow", "risk_level": "varies", "message": "Traffic is being allowed - ensure this is intentional and secure.", "recommendations": ["Review allow rules regularly", "Monitor allowed traffic", "Implement least privilege"]}], "overall_risk": "medium"}, "flow_4": {"flow_id": "flow_4", "flow_data": {"Source IP": "***********", "Destination IP": "***********0", "Port": "444", "Service": "ssh", "Action": "Allow"}, "issues": [{"field": "Service", "value": "ssh", "category": "risky", "risk_level": "high", "message": "Service 'ssh' is classified as risky"}, {"field": "Action", "value": "Allow", "category": "concerning", "risk_level": "medium", "message": "Action 'Allow' is classified as concerning"}], "guidance": [{"field": "Source IP", "value": "***********", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Destination IP", "value": "***********0", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Port", "value": "444", "risk_level": "unknown", "message": "No security issues detected for this value.", "recommendations": ["Continue monitoring", "Review periodically"]}, {"field": "Service", "value": "ssh", "risk_level": "medium", "message": "SSH is a risky protocol when detected. Ensure proper authentication and monitoring.", "recommendations": ["Enable key-based authentication", "Monitor for brute force attempts", "Restrict source IPs"]}, {"field": "Action", "value": "Allow", "risk_level": "varies", "message": "Traffic is being allowed - ensure this is intentional and secure.", "recommendations": ["Review allow rules regularly", "Monitor allowed traffic", "Implement least privilege"]}], "overall_risk": "high"}}, "summary": ["Analyzed 4 flows, 4 flows have security issues", "🟠 HIGH: 1 high-risk issues found", "🟡 MEDIUM: 4 medium-risk issues found"]}