metadata:
  generated: '2025-07-01 14:06:28'
  csv_file: Example.csv
  validation_engine: CSV Security Validator v1.0
  total_flows: 4
  flows_with_issues: 4
summary:
  critical_issues: 0
  high_risk_issues: 1
  medium_risk_issues: 4
  low_risk_issues: 0
  compliance_rate: 0.0
validation_summary:
- Analyzed 4 flows, 4 flows have security issues
- '🟠 HIGH: 1 high-risk issues found'
- '🟡 MEDIUM: 4 medium-risk issues found'
flows:
  flow_1:
    flow_details:
      source_ip: ***********
      destination_ip: ***********0
      port: '111'
      service: https
      action: Allow
    security_assessment:
      overall_risk: medium
      issues_count: 1
      has_guidance: true
    issues:
    - field: Action
      message: Action 'Allow' is classified as concerning
      risk_level: medium
      category: concerning
    security_guidance:
    - field: Source IP
      message: No security issues detected for this value.
      recommendations: &id001
      - Continue monitoring
      - Review periodically
    - field: Destination IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Port
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Service
      message: HTTPS is generally secure but monitor for suspicious destinations.
      recommendations: &id002
      - Monitor certificate validity
      - Check destination reputation
    - field: Action
      message: Traffic is being allowed - ensure this is intentional and secure.
      recommendations: &id003
      - Review allow rules regularly
      - Monitor allowed traffic
      - Implement least privilege
  flow_2:
    flow_details:
      source_ip: ***********
      destination_ip: ***********0
      port: '222'
      service: https
      action: Allow
    security_assessment:
      overall_risk: medium
      issues_count: 1
      has_guidance: true
    issues:
    - field: Action
      message: Action 'Allow' is classified as concerning
      risk_level: medium
      category: concerning
    security_guidance:
    - field: Source IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Destination IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Port
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Service
      message: HTTPS is generally secure but monitor for suspicious destinations.
      recommendations: *id002
    - field: Action
      message: Traffic is being allowed - ensure this is intentional and secure.
      recommendations: *id003
  flow_3:
    flow_details:
      source_ip: ***********
      destination_ip: ***********0
      port: '333'
      service: https
      action: Allow
    security_assessment:
      overall_risk: medium
      issues_count: 1
      has_guidance: true
    issues:
    - field: Action
      message: Action 'Allow' is classified as concerning
      risk_level: medium
      category: concerning
    security_guidance:
    - field: Source IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Destination IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Port
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Service
      message: HTTPS is generally secure but monitor for suspicious destinations.
      recommendations: *id002
    - field: Action
      message: Traffic is being allowed - ensure this is intentional and secure.
      recommendations: *id003
  flow_4:
    flow_details:
      source_ip: ***********
      destination_ip: ***********0
      port: '444'
      service: ssh
      action: Allow
    security_assessment:
      overall_risk: high
      issues_count: 2
      has_guidance: true
    issues:
    - field: Service
      message: Service 'ssh' is classified as risky
      risk_level: high
      category: risky
    - field: Action
      message: Action 'Allow' is classified as concerning
      risk_level: medium
      category: concerning
    security_guidance:
    - field: Source IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Destination IP
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Port
      message: No security issues detected for this value.
      recommendations: *id001
    - field: Service
      message: SSH is a risky protocol when detected. Ensure proper authentication
        and monitoring.
      recommendations:
      - Enable key-based authentication
      - Monitor for brute force attempts
      - Restrict source IPs
    - field: Action
      message: Traffic is being allowed - ensure this is intentional and secure.
      recommendations: *id003
