#!/usr/bin/env python3
"""
YAML Processor - Load and iterate through YAML files exported from CSV data.
Demonstrates various ways to extract and process values from YAML files.
"""

def load_yaml_data(yaml_file: str) -> dict:
    """
    Load YAML data with fallback for systems without PyYAML.
    
    Args:
        yaml_file: Path to YAML file
        
    Returns:
        dict: Parsed YAML data
    """
    try:
        # Try using PyYAML if available
        import yaml
        with open(yaml_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
            
    except ImportError:
        # Fallback: Simple YAML parser for basic structure
        print("⚠️  PyYAML not available, using simple parser")
        return parse_simple_yaml(yaml_file)

def parse_simple_yaml(yaml_file: str) -> dict:
    """
    Simple YAML parser for basic key-value structures.
    Works without PyYAML dependency.
    """
    data = {'metadata': {}, 'flows': {}}
    current_flow = None
    
    with open(yaml_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Parse metadata section
            if line.startswith('metadata:'):
                section = 'metadata'
            elif line.startswith('flows:'):
                section = 'flows'
            elif line.startswith('  ') and ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip().strip("'\"")
                
                if section == 'metadata':
                    data['metadata'][key] = value
                elif section == 'flows' and not key.startswith(' '):
                    # New flow
                    current_flow = key
                    data['flows'][current_flow] = {}
                elif current_flow and key.startswith(' '):
                    # Flow attribute
                    attr_key = key.strip()
                    data['flows'][current_flow][attr_key] = value
    
    return data

def iterate_flows(yaml_data: dict):
    """
    Iterate through all flows and print their details.
    
    Args:
        yaml_data: Loaded YAML data
    """
    print("🔄 Iterating through all flows:")
    print("-" * 40)
    
    flows = yaml_data.get('flows', {})
    for flow_id, flow_details in flows.items():
        print(f"\n📋 {flow_id}:")
        for key, value in flow_details.items():
            print(f"  {key}: {value}")

def find_flows_by_service(yaml_data: dict, service: str) -> list:
    """
    Find all flows that use a specific service.
    
    Args:
        yaml_data: Loaded YAML data
        service: Service to search for (e.g., 'ssh', 'https')
        
    Returns:
        list: List of flow IDs that match the service
    """
    matching_flows = []
    flows = yaml_data.get('flows', {})
    
    for flow_id, flow_details in flows.items():
        if flow_details.get('service', '').lower() == service.lower():
            matching_flows.append(flow_id)
    
    return matching_flows

def find_flows_by_criteria(yaml_data: dict, **criteria) -> list:
    """
    Find flows that match multiple criteria.
    
    Args:
        yaml_data: Loaded YAML data
        **criteria: Key-value pairs to match (e.g., service='ssh', action='Allow')
        
    Returns:
        list: List of flow IDs that match all criteria
    """
    matching_flows = []
    flows = yaml_data.get('flows', {})
    
    for flow_id, flow_details in flows.items():
        match = True
        for key, value in criteria.items():
            if flow_details.get(key, '').lower() != str(value).lower():
                match = False
                break
        
        if match:
            matching_flows.append(flow_id)
    
    return matching_flows

def get_unique_values(yaml_data: dict, field: str) -> set:
    """
    Get all unique values for a specific field across all flows.
    
    Args:
        yaml_data: Loaded YAML data
        field: Field name to extract values from
        
    Returns:
        set: Unique values for the field
    """
    values = set()
    flows = yaml_data.get('flows', {})
    
    for flow_details in flows.values():
        if field in flow_details:
            values.add(flow_details[field])
    
    return values

def extract_ip_ranges(yaml_data: dict) -> dict:
    """
    Extract and analyze IP address ranges from the flows.
    
    Args:
        yaml_data: Loaded YAML data
        
    Returns:
        dict: Analysis of IP ranges
    """
    source_ips = set()
    dest_ips = set()
    flows = yaml_data.get('flows', {})
    
    for flow_details in flows.values():
        if 'source_ip' in flow_details:
            source_ips.add(flow_details['source_ip'])
        if 'destination_ip' in flow_details:
            dest_ips.add(flow_details['destination_ip'])
    
    return {
        'source_ips': sorted(source_ips),
        'destination_ips': sorted(dest_ips),
        'total_unique_sources': len(source_ips),
        'total_unique_destinations': len(dest_ips)
    }

def generate_flow_summary(yaml_data: dict) -> dict:
    """
    Generate a comprehensive summary of the flow data.
    
    Args:
        yaml_data: Loaded YAML data
        
    Returns:
        dict: Summary statistics
    """
    flows = yaml_data.get('flows', {})
    metadata = yaml_data.get('metadata', {})
    
    # Count by service
    services = {}
    actions = {}
    ports = {}
    
    for flow_details in flows.values():
        # Count services
        service = flow_details.get('service', 'unknown')
        services[service] = services.get(service, 0) + 1
        
        # Count actions
        action = flow_details.get('action', 'unknown')
        actions[action] = actions.get(action, 0) + 1
        
        # Count ports
        port = flow_details.get('port', 'unknown')
        ports[port] = ports.get(port, 0) + 1
    
    return {
        'metadata': metadata,
        'total_flows': len(flows),
        'services': services,
        'actions': actions,
        'ports': ports,
        'unique_services': len(services),
        'unique_ports': len(ports)
    }

def main():
    """Demonstrate YAML processing capabilities."""
    
    print("📁 YAML Data Processor")
    print("=" * 50)
    
    # Load YAML file
    yaml_file = "csv_data_export.yaml"
    
    try:
        print(f"Loading YAML file: {yaml_file}")
        yaml_data = load_yaml_data(yaml_file)
        print(f"✅ Loaded successfully")
        
        # Show metadata
        metadata = yaml_data.get('metadata', {})
        print(f"\n📊 Metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        # Iterate through all flows
        iterate_flows(yaml_data)
        
        # Find specific flows
        print(f"\n🔍 Finding flows by service:")
        ssh_flows = find_flows_by_service(yaml_data, 'ssh')
        https_flows = find_flows_by_service(yaml_data, 'https')
        print(f"  SSH flows: {ssh_flows}")
        print(f"  HTTPS flows: {https_flows}")
        
        # Find flows by multiple criteria
        print(f"\n🔍 Finding flows with multiple criteria:")
        allow_flows = find_flows_by_criteria(yaml_data, action='Allow')
        ssh_allow_flows = find_flows_by_criteria(yaml_data, service='ssh', action='Allow')
        print(f"  Allow flows: {allow_flows}")
        print(f"  SSH + Allow flows: {ssh_allow_flows}")
        
        # Get unique values
        print(f"\n📋 Unique values:")
        unique_services = get_unique_values(yaml_data, 'service')
        unique_actions = get_unique_values(yaml_data, 'action')
        print(f"  Services: {sorted(unique_services)}")
        print(f"  Actions: {sorted(unique_actions)}")
        
        # IP analysis
        print(f"\n🌐 IP Address Analysis:")
        ip_analysis = extract_ip_ranges(yaml_data)
        print(f"  Source IPs: {ip_analysis['source_ips']}")
        print(f"  Destination IPs: {ip_analysis['destination_ips']}")
        print(f"  Unique sources: {ip_analysis['total_unique_sources']}")
        print(f"  Unique destinations: {ip_analysis['total_unique_destinations']}")
        
        # Generate summary
        print(f"\n📈 Flow Summary:")
        summary = generate_flow_summary(yaml_data)
        print(f"  Total flows: {summary['total_flows']}")
        print(f"  Services: {summary['services']}")
        print(f"  Actions: {summary['actions']}")
        print(f"  Unique services: {summary['unique_services']}")
        print(f"  Unique ports: {summary['unique_ports']}")
        
    except FileNotFoundError:
        print(f"❌ Error: YAML file '{yaml_file}' not found")
        print("Run csv_to_yaml_example.py first to generate the YAML file")
        
    except Exception as e:
        print(f"❌ Error processing YAML: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
