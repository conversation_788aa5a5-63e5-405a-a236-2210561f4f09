#!/usr/bin/env python3
"""
YAML Template <PERSON>ript - Customize this for your specific YAML processing needs.
This template provides common patterns for loading and processing YAML data.
"""

def load_yaml_data(yaml_file: str) -> dict:
    """
    Load YAML data with fallback support.
    
    Args:
        yaml_file: Path to YAML file
        
    Returns:
        dict: Parsed YAML data
    """
    try:
        # Try PyYAML first (recommended)
        import yaml
        with open(yaml_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except ImportError:
        # Fallback: Simple parser
        return parse_yaml_simple(yaml_file)

def parse_yaml_simple(yaml_file: str) -> dict:
    """Simple YAML parser for basic structures."""
    data = {'flows': {}}
    current_flow = None
    
    with open(yaml_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if ':' in line and not line.startswith('#'):
                if line.startswith('  ') and current_flow:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip().strip("'\"")
                    data['flows'][current_flow][key] = value
                elif not line.startswith(' ') and line.endswith(':'):
                    current_flow = line.rstrip(':')
                    if current_flow not in ['metadata', 'flows']:
                        data['flows'][current_flow] = {}
    return data

def process_flows(yaml_data: dict):
    """
    Main processing function - customize this for your needs.
    
    Args:
        yaml_data: Loaded YAML data
    """
    flows = yaml_data.get('flows', {})
    
    print(f"Processing {len(flows)} flows...")
    
    # Example 1: Process each flow
    for flow_id, flow_data in flows.items():
        
        # Extract values you need
        source_ip = flow_data.get('source_ip', '')
        dest_ip = flow_data.get('destination_ip', '')
        service = flow_data.get('service', '')
        port = flow_data.get('port', '')
        action = flow_data.get('action', '')
        
        # Your custom logic here
        print(f"Processing {flow_id}:")
        print(f"  {source_ip} -> {dest_ip}:{port} ({service}) [{action}]")
        
        # Example: Check for specific conditions
        if service.lower() == 'ssh':
            print(f"  ⚠️  SSH detected in {flow_id}")
        
        if action.lower() == 'allow':
            print(f"  ✅ Traffic allowed for {flow_id}")

def find_by_criteria(yaml_data: dict, **criteria) -> list:
    """
    Find flows matching specific criteria.
    
    Usage:
        ssh_flows = find_by_criteria(data, service='ssh')
        allow_flows = find_by_criteria(data, action='Allow')
        ssh_allow = find_by_criteria(data, service='ssh', action='Allow')
    
    Args:
        yaml_data: Loaded YAML data
        **criteria: Key-value pairs to match
        
    Returns:
        list: Matching flow IDs
    """
    matching_flows = []
    flows = yaml_data.get('flows', {})
    
    for flow_id, flow_data in flows.items():
        match = True
        for key, value in criteria.items():
            if flow_data.get(key, '').lower() != str(value).lower():
                match = False
                break
        if match:
            matching_flows.append(flow_id)
    
    return matching_flows

def extract_values(yaml_data: dict, field: str) -> list:
    """
    Extract all values for a specific field.
    
    Usage:
        all_services = extract_values(data, 'service')
        all_ports = extract_values(data, 'port')
    
    Args:
        yaml_data: Loaded YAML data
        field: Field name to extract
        
    Returns:
        list: All values for the field
    """
    values = []
    flows = yaml_data.get('flows', {})
    
    for flow_data in flows.values():
        if field in flow_data:
            values.append(flow_data[field])
    
    return values

def custom_analysis(yaml_data: dict):
    """
    Add your custom analysis logic here.
    
    Args:
        yaml_data: Loaded YAML data
    """
    flows = yaml_data.get('flows', {})
    
    # Example: Count flows by service
    service_counts = {}
    for flow_data in flows.values():
        service = flow_data.get('service', 'unknown')
        service_counts[service] = service_counts.get(service, 0) + 1
    
    print("\nService Analysis:")
    for service, count in sorted(service_counts.items()):
        print(f"  {service}: {count} flows")
    
    # Example: Find high-risk scenarios
    risky_flows = find_by_criteria(yaml_data, service='ssh', action='Allow')
    if risky_flows:
        print(f"\n⚠️  High-risk flows (SSH + Allow): {risky_flows}")
    
    # Example: IP analysis
    source_ips = extract_values(yaml_data, 'source_ip')
    unique_sources = len(set(source_ips))
    print(f"\nIP Analysis:")
    print(f"  Total flows: {len(source_ips)}")
    print(f"  Unique source IPs: {unique_sources}")

def main():
    """Main execution function - customize as needed."""
    
    # Configuration
    yaml_file = "csv_data_export.yaml"  # Change this to your YAML file
    
    print("🔍 YAML Processing Script")
    print("=" * 40)
    
    try:
        # Load YAML data
        print(f"Loading: {yaml_file}")
        yaml_data = load_yaml_data(yaml_file)
        print("✅ Data loaded successfully")
        
        # Process the data
        process_flows(yaml_data)
        
        # Run custom analysis
        custom_analysis(yaml_data)
        
        # Example queries
        print(f"\n🔍 Example Queries:")
        ssh_flows = find_by_criteria(yaml_data, service='ssh')
        https_flows = find_by_criteria(yaml_data, service='https')
        allow_flows = find_by_criteria(yaml_data, action='Allow')
        
        print(f"  SSH flows: {ssh_flows}")
        print(f"  HTTPS flows: {https_flows}")
        print(f"  Allow flows: {allow_flows}")
        
        # Extract specific values
        all_services = extract_values(yaml_data, 'service')
        all_ports = extract_values(yaml_data, 'port')
        
        print(f"\n📊 Data Summary:")
        print(f"  Services: {sorted(set(all_services))}")
        print(f"  Ports: {sorted(set(all_ports))}")
        
        print(f"\n✅ Processing completed successfully!")
        
    except FileNotFoundError:
        print(f"❌ Error: File '{yaml_file}' not found")
        print("Make sure the YAML file exists or update the yaml_file variable")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

# ============================================================================
# CUSTOMIZATION EXAMPLES:
# ============================================================================

# Example 1: Find flows with specific IP ranges
def find_ip_range_flows(yaml_data: dict, ip_prefix: str) -> list:
    """Find flows with IPs starting with specific prefix."""
    matching_flows = []
    flows = yaml_data.get('flows', {})
    
    for flow_id, flow_data in flows.items():
        source_ip = flow_data.get('source_ip', '')
        dest_ip = flow_data.get('destination_ip', '')
        
        if source_ip.startswith(ip_prefix) or dest_ip.startswith(ip_prefix):
            matching_flows.append(flow_id)
    
    return matching_flows

# Example 2: Generate CSV output from YAML
def export_to_csv(yaml_data: dict, output_file: str):
    """Export YAML data back to CSV format."""
    flows = yaml_data.get('flows', {})
    
    with open(output_file, 'w', encoding='utf-8') as f:
        # Write header
        f.write("Flow ID,Source IP,Destination IP,Port,Service,Action\n")
        
        # Write data
        for flow_id, flow_data in flows.items():
            source_ip = flow_data.get('source_ip', '')
            dest_ip = flow_data.get('destination_ip', '')
            port = flow_data.get('port', '')
            service = flow_data.get('service', '')
            action = flow_data.get('action', '')
            
            f.write(f"{flow_id},{source_ip},{dest_ip},{port},{service},{action}\n")

# Example 3: Filter and create new YAML
def create_filtered_yaml(yaml_data: dict, criteria: dict, output_file: str):
    """Create a new YAML file with filtered flows."""
    matching_flows = find_by_criteria(yaml_data, **criteria)
    flows = yaml_data.get('flows', {})
    
    filtered_data = {
        'metadata': {
            'exported': yaml_data.get('metadata', {}).get('exported', ''),
            'total_flows': len(matching_flows),
            'export_type': 'Filtered Export',
            'filter_criteria': criteria
        },
        'flows': {flow_id: flows[flow_id] for flow_id in matching_flows if flow_id in flows}
    }
    
    # Save filtered data (you can use the export_csv_data_to_yaml function here)
    print(f"Filtered {len(matching_flows)} flows matching criteria: {criteria}")
    return filtered_data
